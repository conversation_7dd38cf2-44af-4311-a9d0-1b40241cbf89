package com.monsociete.dashbordassurance.service;

import com.monsociete.dashbordassurance.entity.Utilisateur;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Service
public class JwtService {
    
    private static final String SECRET_KEY = "dashboardAssuranceSecretKeyForJWTTokenGeneration2024";
    private static final int JWT_EXPIRATION = 86400000; // 24 heures en millisecondes
    
    public String extractEmail(String token) {
        return extractClaim(token, Claims::getSubject);
    }
    
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }
    
    public String generateToken(Utilisateur utilisateur) {
        return generateToken(new HashMap<>(), utilisateur);
    }
    
    public String generateToken(Map<String, Object> extraClaims, Utilisateur utilisateur) {
        extraClaims.put("userId", utilisateur.getId());
        extraClaims.put("role", utilisateur.getRole().toString());
        extraClaims.put("nom", utilisateur.getNomComplet());
        
        return Jwts
                .builder()
                .setClaims(extraClaims)
                .setSubject(utilisateur.getEmail())
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + JWT_EXPIRATION))
                .signWith(getSignInKey(), SignatureAlgorithm.HS256)
                .compact();
    }
    
    public boolean isTokenValid(String token, Utilisateur utilisateur) {
        final String email = extractEmail(token);
        return (email.equals(utilisateur.getEmail())) && !isTokenExpired(token);
    }
    
    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }
    
    private Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }
    
    private Claims extractAllClaims(String token) {
        return Jwts
                .parserBuilder()
                .setSigningKey(getSignInKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
    
    private Key getSignInKey() {
        byte[] keyBytes = SECRET_KEY.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }
    
    public Long extractUserId(String token) {
        Claims claims = extractAllClaims(token);
        return claims.get("userId", Long.class);
    }
    
    public String extractRole(String token) {
        Claims claims = extractAllClaims(token);
        return claims.get("role", String.class);
    }
    
    public String extractNom(String token) {
        Claims claims = extractAllClaims(token);
        return claims.get("nom", String.class);
    }
}
