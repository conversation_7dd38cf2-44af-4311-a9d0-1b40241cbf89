package com.monsociete.dashbordassurance.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "utilisateurs")
public class Utilisateur {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String prenom;
    
    @Column(nullable = false)
    private String nom;
    
    @Column(unique = true, nullable = false)
    private String email;
    
    @Column(nullable = false)
    private String motDePasse;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Role role;
    
    @Column(nullable = false)
    private Boolean actif = true;
    
    @Column
    private String telephone;
    
    @Column
    private String adresse;
    
    @Column(name = "date_creation")
    private LocalDateTime dateCreation;
    
    @Column(name = "derniere_connexion")
    private LocalDateTime derniereConnexion;
    
    @Column(name = "tentatives_connexion")
    private Integer tentativesConnexion = 0;
    
    @Column(name = "compte_verrouille")
    private Boolean compteVerrouille = false;
    
    // Enum pour les rôles
    public enum Role {
        CLIENT, CONSEILLER, GESTIONNAIRE, ADMIN
    }
    
    // Constructeurs
    public Utilisateur() {
        this.dateCreation = LocalDateTime.now();
    }
    
    public Utilisateur(String prenom, String nom, String email, String motDePasse, Role role) {
        this();
        this.prenom = prenom;
        this.nom = nom;
        this.email = email;
        this.motDePasse = motDePasse;
        this.role = role;
    }
    
    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getPrenom() { return prenom; }
    public void setPrenom(String prenom) { this.prenom = prenom; }
    
    public String getNom() { return nom; }
    public void setNom(String nom) { this.nom = nom; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getMotDePasse() { return motDePasse; }
    public void setMotDePasse(String motDePasse) { this.motDePasse = motDePasse; }
    
    public Role getRole() { return role; }
    public void setRole(Role role) { this.role = role; }
    
    public Boolean getActif() { return actif; }
    public void setActif(Boolean actif) { this.actif = actif; }
    
    public String getTelephone() { return telephone; }
    public void setTelephone(String telephone) { this.telephone = telephone; }
    
    public String getAdresse() { return adresse; }
    public void setAdresse(String adresse) { this.adresse = adresse; }
    
    public LocalDateTime getDateCreation() { return dateCreation; }
    public void setDateCreation(LocalDateTime dateCreation) { this.dateCreation = dateCreation; }
    
    public LocalDateTime getDerniereConnexion() { return derniereConnexion; }
    public void setDerniereConnexion(LocalDateTime derniereConnexion) { this.derniereConnexion = derniereConnexion; }
    
    public Integer getTentativesConnexion() { return tentativesConnexion; }
    public void setTentativesConnexion(Integer tentativesConnexion) { this.tentativesConnexion = tentativesConnexion; }
    
    public Boolean getCompteVerrouille() { return compteVerrouille; }
    public void setCompteVerrouille(Boolean compteVerrouille) { this.compteVerrouille = compteVerrouille; }
    
    // Méthodes utilitaires
    public String getNomComplet() {
        return prenom + " " + nom;
    }
    
    public boolean isAdmin() {
        return role == Role.ADMIN;
    }
    
    public boolean isGestionnaire() {
        return role == Role.GESTIONNAIRE || role == Role.ADMIN;
    }
    
    public boolean isConseiller() {
        return role == Role.CONSEILLER || role == Role.GESTIONNAIRE || role == Role.ADMIN;
    }
    
    public boolean isClient() {
        return role == Role.CLIENT;
    }
}
