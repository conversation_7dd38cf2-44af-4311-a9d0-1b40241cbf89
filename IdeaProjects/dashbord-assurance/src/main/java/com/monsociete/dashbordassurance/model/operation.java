package com.monsociete.dashbordassurance.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "operations")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Operation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TypeOperation type;

    @NotNull(message = "Le montant est obligatoire")
    @Positive(message = "Le montant doit être positif")
    @Column(nullable = false, precision = 15, scale = 2)
    private BigDecimal montant;

    @NotNull(message = "La date est obligatoire")
    @Column(nullable = false)
    private LocalDate date;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private CanalOperation canal = CanalOperation.AGENCE;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatutOperation statut = StatutOperation.EN_COURS;

    @Column(length = 500)
    private String commentaire;

    @Column(name = "reference_externe", length = 100)
    private String referenceExterne;

    @Column(name = "date_creation", nullable = false)
    private LocalDateTime dateCreation;

    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    @Column(name = "date_validation")
    private LocalDateTime dateValidation;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contrat_id", nullable = false)
    private Contrat contrat;

    // Enums
    public enum TypeOperation {
        VERSEMENT("Versement"),
        RACHAT("Rachat"),
        ARBITRAGE("Arbitrage"),
        AVANCE("Avance"),
        REMBOURSEMENT("Remboursement"),
        TRANSFERT("Transfert");

        private final String libelle;

        TypeOperation(String libelle) {
            this.libelle = libelle;
        }

        public String getLibelle() {
            return libelle;
        }
    }

    public enum CanalOperation {
        AGENCE("Agence"),
        INTERNET("Internet"),
        TELEPHONE("Téléphone"),
        COURRIER("Courrier"),
        MOBILE("Application Mobile");

        private final String libelle;

        CanalOperation(String libelle) {
            this.libelle = libelle;
        }

        public String getLibelle() {
            return libelle;
        }
    }

    public enum StatutOperation {
        EN_COURS("En cours"),
        VALIDEE("Validée"),
        REJETEE("Rejetée"),
        ANNULEE("Annulée"),
        EN_ATTENTE("En attente");

        private final String libelle;

        StatutOperation(String libelle) {
            this.libelle = libelle;
        }

        public String getLibelle() {
            return libelle;
        }
    }

    // Méthodes utilitaires
    @PrePersist
    protected void onCreate() {
        dateCreation = LocalDateTime.now();
        dateModification = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        dateModification = LocalDateTime.now();
    }

    public boolean isValidee() {
        return statut == StatutOperation.VALIDEE;
    }

    public boolean isEnCours() {
        return statut == StatutOperation.EN_COURS;
    }

    public String getClientNom() {
        return contrat != null && contrat.getUtilisateur() != null ?
               contrat.getUtilisateur().getNomComplet() : "N/A";
    }

    public String getProduitNom() {
        return contrat != null ? contrat.getProduit() : "N/A";
    }

    // Constructeur pour création rapide
    public Operation(TypeOperation type, BigDecimal montant, LocalDate date,
                     Contrat contrat, CanalOperation canal) {
        this.type = type;
        this.montant = montant;
        this.date = date;
        this.contrat = contrat;
        this.canal = canal;
        this.statut = StatutOperation.EN_COURS;
    }
}
