package com.monsociete.dashbordassurance.service;

import com.monsociete.dashbordassurance.model.Contrat;
import com.monsociete.dashbordassurance.model.Utilisateur;
import com.monsociete.dashbordassurance.repository.ContratRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ContratService {

    @Autowired
    private ContratRepository contratRepository;

    @Autowired
    private UtilisateurService utilisateurService;

    // Récupérer tous les contrats
    public List<Contrat> getAllContrats() {
        return contratRepository.findAll();
    }

    // Récupérer un contrat par ID
    public Optional<Contrat> getContratById(Long id) {
        return contratRepository.findById(id);
    }

    // Récupérer un contrat par numéro
    public Optional<Contrat> getContratByNumero(String numeroContrat) {
        return contratRepository.findByNumeroContrat(numeroContrat);
    }

    // Créer un nouveau contrat
    public Contrat createContrat(Contrat contrat) {
        // Vérifier si le numéro de contrat existe déjà
        if (contratRepository.existsByNumeroContrat(contrat.getNumeroContrat())) {
            throw new RuntimeException("Un contrat avec ce numéro existe déjà");
        }

        // Vérifier que l'utilisateur existe
        if (contrat.getUtilisateur() != null && contrat.getUtilisateur().getId() != null) {
            Utilisateur utilisateur = utilisateurService.getUtilisateurById(contrat.getUtilisateur().getId())
                    .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé"));
            contrat.setUtilisateur(utilisateur);
        }

        return contratRepository.save(contrat);
    }

    // Mettre à jour un contrat
    public Contrat updateContrat(Long id, Contrat contratDetails) {
        return contratRepository.findById(id)
                .map(contrat -> {
                    contrat.setProduit(contratDetails.getProduit());
                    contrat.setTypeProduit(contratDetails.getTypeProduit());
                    contrat.setMontantActuel(contratDetails.getMontantActuel());
                    contrat.setStatut(contratDetails.getStatut());
                    contrat.setTauxRendement(contratDetails.getTauxRendement());
                    contrat.setDescription(contratDetails.getDescription());
                    contrat.setDateCloture(contratDetails.getDateCloture());
                    
                    return contratRepository.save(contrat);
                })
                .orElseThrow(() -> new RuntimeException("Contrat non trouvé avec l'ID: " + id));
    }

    // Clôturer un contrat
    public void cloturerContrat(Long id, LocalDate dateCloture) {
        contratRepository.findById(id)
                .map(contrat -> {
                    contrat.setStatut(Contrat.StatutContrat.CLOTURE);
                    contrat.setDateCloture(dateCloture != null ? dateCloture : LocalDate.now());
                    return contratRepository.save(contrat);
                })
                .orElseThrow(() -> new RuntimeException("Contrat non trouvé avec l'ID: " + id));
    }

    // Suspendre un contrat
    public void suspendreContrat(Long id) {
        contratRepository.findById(id)
                .map(contrat -> {
                    contrat.setStatut(Contrat.StatutContrat.SUSPENDU);
                    return contratRepository.save(contrat);
                })
                .orElseThrow(() -> new RuntimeException("Contrat non trouvé avec l'ID: " + id));
    }

    // Réactiver un contrat
    public void reactiverContrat(Long id) {
        contratRepository.findById(id)
                .map(contrat -> {
                    contrat.setStatut(Contrat.StatutContrat.ACTIF);
                    return contratRepository.save(contrat);
                })
                .orElseThrow(() -> new RuntimeException("Contrat non trouvé avec l'ID: " + id));
    }

    // Supprimer un contrat (hard delete - à utiliser avec précaution)
    public void deleteContrat(Long id) {
        if (!contratRepository.existsById(id)) {
            throw new RuntimeException("Contrat non trouvé avec l'ID: " + id);
        }
        contratRepository.deleteById(id);
    }

    // Récupérer les contrats d'un utilisateur
    public List<Contrat> getContratsByUtilisateur(Long utilisateurId) {
        Utilisateur utilisateur = utilisateurService.getUtilisateurById(utilisateurId)
                .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé"));
        return contratRepository.findByUtilisateur(utilisateur);
    }

    // Récupérer les contrats actifs d'un utilisateur
    public List<Contrat> getContratsActifsByUtilisateur(Long utilisateurId) {
        Utilisateur utilisateur = utilisateurService.getUtilisateurById(utilisateurId)
                .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé"));
        return contratRepository.findByUtilisateurAndStatut(utilisateur, Contrat.StatutContrat.ACTIF);
    }

    // Récupérer les contrats par statut
    public List<Contrat> getContratsByStatut(Contrat.StatutContrat statut) {
        return contratRepository.findByStatut(statut);
    }

    // Récupérer les contrats par type de produit
    public List<Contrat> getContratsByTypeProduit(Contrat.TypeProduit typeProduit) {
        return contratRepository.findByTypeProduit(typeProduit);
    }

    // Rechercher des contrats avec filtres
    public List<Contrat> searchContrats(Long utilisateurId, Contrat.StatutContrat statut,
                                       Contrat.TypeProduit typeProduit, String produit,
                                       LocalDate dateOuvertureDebut, LocalDate dateOuvertureFin,
                                       BigDecimal montantMin, BigDecimal montantMax) {
        return contratRepository.findContratsWithFilters(
                utilisateurId, statut, typeProduit, produit,
                dateOuvertureDebut, dateOuvertureFin, montantMin, montantMax
        );
    }

    // Récupérer les contrats par email utilisateur
    public List<Contrat> getContratsByUtilisateurEmail(String email) {
        return contratRepository.findByUtilisateurEmail(email);
    }

    // Statistiques par type de produit
    public List<Object[]> getStatistiquesByTypeProduit() {
        return contratRepository.getStatistiquesByTypeProduit();
    }

    // Contrats avec le plus d'opérations
    public List<Object[]> getContratsWithMostOperations() {
        return contratRepository.getContratsWithMostOperations();
    }

    // Montant total par utilisateur
    public List<Object[]> getMontantTotalByUtilisateur() {
        return contratRepository.getMontantTotalByUtilisateur();
    }

    // Contrats proches de l'échéance
    public List<Contrat> getContratsProchesEcheance(int annees) {
        LocalDate dateLimit = LocalDate.now().minusYears(annees);
        return contratRepository.findContratsProchesEcheance(dateLimit);
    }

    // Vérifier si un contrat existe
    public boolean existsById(Long id) {
        return contratRepository.existsById(id);
    }

    // Vérifier si un numéro de contrat existe
    public boolean existsByNumeroContrat(String numeroContrat) {
        return contratRepository.existsByNumeroContrat(numeroContrat);
    }

    // Mettre à jour le montant actuel d'un contrat
    public void updateMontantActuel(Long contratId, BigDecimal nouveauMontant) {
        contratRepository.findById(contratId)
                .map(contrat -> {
                    contrat.setMontantActuel(nouveauMontant);
                    return contratRepository.save(contrat);
                })
                .orElseThrow(() -> new RuntimeException("Contrat non trouvé avec l'ID: " + contratId));
    }
}
