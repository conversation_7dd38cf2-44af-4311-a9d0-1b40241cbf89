package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.service.OperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/statistics")
@CrossOrigin(origins = "*")
public class StatisticsController {

    @Autowired
    private OperationService operationService;

    // Statistiques par type d'opération
    @GetMapping("/by-type")
    public Map<String, Object> getStatisticsByType(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end) {
        
        List<Operation> operations;
        if (start != null && end != null) {
            operations = operationService.filter(null, null, null, start, end);
        } else {
            operations = operationService.getAll();
        }

        Map<String, Long> countByType = operations.stream()
                .collect(Collectors.groupingBy(Operation::getType, Collectors.counting()));

        Map<String, Double> amountByType = operations.stream()
                .collect(Collectors.groupingBy(Operation::getType, 
                    Collectors.summingDouble(Operation::getMontant)));

        Map<String, Object> result = new HashMap<>();
        result.put("count", countByType);
        result.put("amount", amountByType);
        
        return result;
    }

    // Statistiques par client
    @GetMapping("/by-client")
    public Map<String, Object> getStatisticsByClient(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end) {
        
        List<Operation> operations;
        if (start != null && end != null) {
            operations = operationService.filter(null, null, null, start, end);
        } else {
            operations = operationService.getAll();
        }

        Map<String, Long> countByClient = operations.stream()
                .collect(Collectors.groupingBy(Operation::getClient, Collectors.counting()));

        Map<String, Double> amountByClient = operations.stream()
                .collect(Collectors.groupingBy(Operation::getClient, 
                    Collectors.summingDouble(Operation::getMontant)));

        Map<String, Object> result = new HashMap<>();
        result.put("count", countByClient);
        result.put("amount", amountByClient);
        
        return result;
    }

    // Statistiques par produit
    @GetMapping("/by-product")
    public Map<String, Object> getStatisticsByProduct(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end) {
        
        List<Operation> operations;
        if (start != null && end != null) {
            operations = operationService.filter(null, null, null, start, end);
        } else {
            operations = operationService.getAll();
        }

        Map<String, Long> countByProduct = operations.stream()
                .collect(Collectors.groupingBy(Operation::getProduit, Collectors.counting()));

        Map<String, Double> amountByProduct = operations.stream()
                .collect(Collectors.groupingBy(Operation::getProduit, 
                    Collectors.summingDouble(Operation::getMontant)));

        Map<String, Object> result = new HashMap<>();
        result.put("count", countByProduct);
        result.put("amount", amountByProduct);
        
        return result;
    }

    // Évolution temporelle des opérations
    @GetMapping("/timeline")
    public Map<String, Object> getTimelineStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end) {
        
        List<Operation> operations;
        if (start != null && end != null) {
            operations = operationService.filter(null, null, null, start, end);
        } else {
            operations = operationService.getAll();
        }

        Map<LocalDate, Long> countByDate = operations.stream()
                .collect(Collectors.groupingBy(Operation::getDate, Collectors.counting()));

        Map<LocalDate, Double> amountByDate = operations.stream()
                .collect(Collectors.groupingBy(Operation::getDate, 
                    Collectors.summingDouble(Operation::getMontant)));

        Map<String, Object> result = new HashMap<>();
        result.put("count", countByDate);
        result.put("amount", amountByDate);
        
        return result;
    }

    // KPIs généraux
    @GetMapping("/kpis")
    public Map<String, Object> getKPIs(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end) {
        
        List<Operation> operations;
        if (start != null && end != null) {
            operations = operationService.filter(null, null, null, start, end);
        } else {
            operations = operationService.getAll();
        }

        Map<String, Object> kpis = new HashMap<>();
        kpis.put("totalOperations", operations.size());
        kpis.put("totalAmount", operations.stream().mapToDouble(Operation::getMontant).sum());
        kpis.put("averageAmount", operations.stream().mapToDouble(Operation::getMontant).average().orElse(0.0));
        kpis.put("uniqueClients", operations.stream().map(Operation::getClient).distinct().count());
        kpis.put("uniqueProducts", operations.stream().map(Operation::getProduit).distinct().count());
        
        return kpis;
    }
}
