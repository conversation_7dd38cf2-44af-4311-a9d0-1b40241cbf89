package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

@RestController
@RequestMapping("/api/simple")
@CrossOrigin(origins = "*")
public class SimpleAuthController {

    // Données de test pour les utilisateurs
    private static final List<Map<String, Object>> USERS = new ArrayList<>();
    
    static {
        // Initialiser les utilisateurs de test
        Map<String, Object> admin = new HashMap<>();
        admin.put("id", 1L);
        admin.put("email", "<EMAIL>");
        admin.put("motDePasse", "admin123");
        admin.put("nom", "Admin");
        admin.put("prenom", "Super");
        admin.put("role", "ADMIN");
        USERS.add(admin);
        
        Map<String, Object> gestionnaire = new HashMap<>();
        gestionnaire.put("id", 2L);
        gestionnaire.put("email", "<EMAIL>");
        gestionnaire.put("motDePasse", "gestionnaire123");
        gestionnaire.put("nom", "Gestionnaire");
        gestionnaire.put("prenom", "Jean");
        gestionnaire.put("role", "GESTIONNAIRE");
        USERS.add(gestionnaire);
        
        Map<String, Object> conseiller = new HashMap<>();
        conseiller.put("id", 3L);
        conseiller.put("email", "<EMAIL>");
        conseiller.put("motDePasse", "conseiller123");
        conseiller.put("nom", "Conseiller");
        conseiller.put("prenom", "Marie");
        conseiller.put("role", "CONSEILLER");
        USERS.add(conseiller);
        
        Map<String, Object> client = new HashMap<>();
        client.put("id", 4L);
        client.put("email", "<EMAIL>");
        client.put("motDePasse", "client123");
        client.put("nom", "Client");
        client.put("prenom", "Pierre");
        client.put("role", "CLIENT");
        USERS.add(client);
    }

    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> credentials) {
        String email = credentials.get("email");
        String motDePasse = credentials.get("motDePasse");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            System.out.println("🔐 Tentative de connexion simple pour: " + email);
            
            // Rechercher l'utilisateur par email
            Map<String, Object> utilisateur = USERS.stream()
                    .filter(user -> email.equals(user.get("email")))
                    .findFirst()
                    .orElse(null);
            
            if (utilisateur != null && motDePasse.equals(utilisateur.get("motDePasse"))) {
                // Générer un token simple
                String token = "simple_token_" + utilisateur.get("id") + "_" + System.currentTimeMillis();
                
                response.put("success", true);
                response.put("message", "Connexion réussie");
                response.put("token", token);
                response.put("utilisateur", Map.of(
                    "id", utilisateur.get("id"),
                    "email", utilisateur.get("email"),
                    "nom", utilisateur.get("nom"),
                    "prenom", utilisateur.get("prenom"),
                    "role", utilisateur.get("role")
                ));
                
                System.out.println("✅ Connexion simple réussie pour: " + utilisateur.get("prenom") + " " + utilisateur.get("nom"));
                
            } else {
                System.out.println("❌ Échec de connexion simple pour: " + email);
                response.put("success", false);
                response.put("message", "Email ou mot de passe incorrect");
            }
            
        } catch (Exception e) {
            System.out.println("💥 Erreur de connexion simple: " + e.getMessage());
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "Erreur lors de la connexion: " + e.getMessage());
        }
        
        return response;
    }

    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Statistiques de démonstration
        stats.put("totalOperations", 150);
        stats.put("totalMontant", 2500000.0);
        stats.put("montantMoyen", 16666.67);
        stats.put("clientsUniques", 45);
        
        return stats;
    }

    @GetMapping("/operations")
    public List<Map<String, Object>> getOperations() {
        List<Map<String, Object>> operations = new ArrayList<>();
        
        // Données de démonstration
        for (int i = 1; i <= 20; i++) {
            Map<String, Object> operation = new HashMap<>();
            operation.put("id", i);
            operation.put("type", i % 3 == 0 ? "SOUSCRIPTION" : (i % 2 == 0 ? "SINISTRE" : "AVENANT"));
            operation.put("clientNom", "Client " + i);
            operation.put("produitNom", "Produit " + (i % 5 + 1));
            operation.put("montant", 10000 + (i * 1000));
            operation.put("date", "2024-12-" + String.format("%02d", (i % 28) + 1));
            operation.put("statut", i % 4 == 0 ? "VALIDEE" : (i % 3 == 0 ? "EN_COURS" : "EN_ATTENTE"));
            operation.put("canal", i % 2 == 0 ? "WEB" : "AGENCE");
            operations.add(operation);
        }
        
        return operations;
    }

    @GetMapping("/test")
    public Map<String, Object> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "API Simple fonctionne correctement");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
