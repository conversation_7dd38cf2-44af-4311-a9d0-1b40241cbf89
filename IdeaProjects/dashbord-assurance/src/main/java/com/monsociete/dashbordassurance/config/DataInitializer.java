package com.monsociete.dashbordassurance.config;

import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.repository.OperationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private OperationRepository operationRepository;

    @Override
    public void run(String... args) throws Exception {
        // Vérifier si des données existent déjà
        if (operationRepository.count() == 0) {
            initializeTestData();
        }
    }

    private void initializeTestData() {
        List<String> types = Arrays.asList("rachat", "versement", "arbitrage");
        List<String> clients = Arrays.asList("Client A", "Client B", "Client C", "Client D", "Client E");
        List<String> produits = Arrays.asList("Assurance Vie", "PEA", "Contrat Capitalisation", "PERP", "Multisupport");

        Random random = new Random();
        
        // Générer 100 opérations de test
        for (int i = 0; i < 100; i++) {
            Operation operation = new Operation();
            operation.setType(types.get(random.nextInt(types.size())));
            operation.setClient(clients.get(random.nextInt(clients.size())));
            operation.setProduit(produits.get(random.nextInt(produits.size())));
            operation.setMontant(1000.0 + random.nextDouble() * 50000.0); // Entre 1000 et 51000
            
            // Dates aléatoires dans les 6 derniers mois
            LocalDate startDate = LocalDate.now().minusMonths(6);
            LocalDate endDate = LocalDate.now();
            long daysBetween = endDate.toEpochDay() - startDate.toEpochDay();
            LocalDate randomDate = startDate.plusDays(random.nextLong() % daysBetween);
            operation.setDate(randomDate);
            
            operationRepository.save(operation);
        }
        
        System.out.println("Données de test initialisées : " + operationRepository.count() + " opérations créées.");
    }
}
