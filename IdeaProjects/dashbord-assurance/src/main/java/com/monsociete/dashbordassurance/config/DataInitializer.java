package com.monsociete.dashbordassurance.config;

import com.monsociete.dashbordassurance.entity.Operation;
import com.monsociete.dashbordassurance.entity.Utilisateur;
import com.monsociete.dashbordassurance.repository.OperationRepository;
import com.monsociete.dashbordassurance.repository.UtilisateurRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Random;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private OperationRepository operationRepository;

    @Autowired
    private UtilisateurRepository utilisateurRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public void run(String... args) throws Exception {
        // Initialiser les utilisateurs d'abord
        initializeUsers();

        // Puis les opérations
        initializeOperations();
    }

    private void initializeUsers() {
        // Vérifier si des utilisateurs existent déjà
        if (utilisateurRepository.count() > 0) {
            System.out.println("✅ Utilisateurs déjà présents dans la base de données (" + utilisateurRepository.count() + " utilisateurs)");
            return;
        }

        System.out.println("🔄 Initialisation des utilisateurs...");

        // Créer l'administrateur
        Utilisateur admin = new Utilisateur();
        admin.setPrenom("Admin");
        admin.setNom("Super");
        admin.setEmail("<EMAIL>");
        admin.setMotDePasse(passwordEncoder.encode("admin123"));
        admin.setRole(Utilisateur.Role.ADMIN);
        admin.setTelephone("0123456789");
        admin.setAdresse("1 Rue de l'Administration, 75001 Paris");
        utilisateurRepository.save(admin);

        // Créer le gestionnaire
        Utilisateur gestionnaire = new Utilisateur();
        gestionnaire.setPrenom("Gestionnaire");
        gestionnaire.setNom("Principal");
        gestionnaire.setEmail("<EMAIL>");
        gestionnaire.setMotDePasse(passwordEncoder.encode("gest123"));
        gestionnaire.setRole(Utilisateur.Role.GESTIONNAIRE);
        gestionnaire.setTelephone("0123456790");
        gestionnaire.setAdresse("2 Rue de la Gestion, 75002 Paris");
        utilisateurRepository.save(gestionnaire);

        // Créer le conseiller
        Utilisateur conseiller = new Utilisateur();
        conseiller.setPrenom("Conseiller");
        conseiller.setNom("Expert");
        conseiller.setEmail("<EMAIL>");
        conseiller.setMotDePasse(passwordEncoder.encode("cons123"));
        conseiller.setRole(Utilisateur.Role.CONSEILLER);
        conseiller.setTelephone("0123456791");
        conseiller.setAdresse("3 Rue du Conseil, 75003 Paris");
        utilisateurRepository.save(conseiller);

        // Créer les clients
        String[][] clientsData = {
            {"Jean", "Dupont", "<EMAIL>"},
            {"Marie", "Martin", "<EMAIL>"},
            {"Pierre", "Bernard", "<EMAIL>"},
            {"Sophie", "Dubois", "<EMAIL>"},
            {"Paul", "Thomas", "<EMAIL>"},
            {"Julie", "Robert", "<EMAIL>"},
            {"Michel", "Petit", "<EMAIL>"},
            {"Anne", "Richard", "<EMAIL>"},
            {"François", "Durand", "<EMAIL>"},
            {"Catherine", "Moreau", "<EMAIL>"}
        };

        Random random = new Random(42);
        for (int i = 0; i < clientsData.length; i++) {
            Utilisateur client = new Utilisateur();
            client.setPrenom(clientsData[i][0]);
            client.setNom(clientsData[i][1]);
            client.setEmail(clientsData[i][2]);
            client.setMotDePasse(passwordEncoder.encode("client123"));
            client.setRole(Utilisateur.Role.CLIENT);
            client.setTelephone("0" + (600000000 + i * 1000000 + random.nextInt(999999)));
            client.setAdresse((i + 1) + " Rue de la Paix, 75001 Paris");
            utilisateurRepository.save(client);
        }

        System.out.println("✅ " + utilisateurRepository.count() + " utilisateurs créés avec succès !");
        System.out.println("📋 Comptes de test créés :");
        System.out.println("   - Admin: <EMAIL> / admin123");
        System.out.println("   - Gestionnaire: <EMAIL> / gest123");
        System.out.println("   - Conseiller: <EMAIL> / cons123");
        System.out.println("   - Clients: *.email.com / client123");
    }

    private void initializeOperations() {
        // Vérifier si des données existent déjà
        if (operationRepository.count() > 0) {
            System.out.println("✅ Opérations déjà présentes dans la base de données (" + operationRepository.count() + " opérations)");
            return;
        }

        System.out.println("🔄 Initialisation des opérations...");
        
        String[] types = {"VERSEMENT", "RACHAT", "ARBITRAGE", "AVANCE", "TRANSFERT"};
        String[] clients = {
            "Jean Dupont", "Marie Martin", "Pierre Bernard", "Sophie Dubois", "Paul Thomas",
            "Julie Robert", "Michel Petit", "Anne Richard", "François Durand", "Catherine Moreau",
            "Alain Rousseau", "Sylvie Leroy", "Patrick Moreau"
        };
        String[] produits = {
            "Assurance Vie Premium", "PEA Dynamique", "PERP Sécurisé", 
            "Multisupport Équilibré", "Contrat Capitalisation"
        };
        String[] statuts = {"VALIDEE", "EN_COURS", "EN_ATTENTE", "REJETEE"};
        String[] canaux = {"AGENCE", "INTERNET", "TELEPHONE", "MOBILE"};
        
        Random random = new Random(42); // Seed fixe pour des données reproductibles
        
        for (int i = 1; i <= 150; i++) {
            Operation operation = new Operation();
            
            operation.setType(types[random.nextInt(types.length)]);
            operation.setClientNom(clients[random.nextInt(clients.length)]);
            operation.setProduitNom(produits[random.nextInt(produits.length)]);
            operation.setMontant((double) (1000 + random.nextInt(49000))); // Entre 1000 et 50000
            
            // Date aléatoire dans les 6 derniers mois
            LocalDate baseDate = LocalDate.now().minusDays(180);
            LocalDate randomDate = baseDate.plusDays(random.nextInt(180));
            operation.setDate(randomDate);
            
            operation.setStatut(statuts[random.nextInt(statuts.length)]);
            operation.setCanal(canaux[random.nextInt(canaux.length)]);
            operation.setCommentaire("Opération " + operation.getType() + " via " + operation.getCanal());
            operation.setReferenceExterne("REF" + String.format("%06d", i));
            
            operationRepository.save(operation);
        }
        
        System.out.println("✅ " + operationRepository.count() + " opérations créées avec succès !");
        
        // Afficher quelques statistiques
        System.out.println("📊 Statistiques:");
        System.out.println("   - Total opérations: " + operationRepository.countTotalOperations());
        System.out.println("   - Montant total: " + String.format("%.2f€", operationRepository.sumTotalMontant()));
        System.out.println("   - Montant moyen: " + String.format("%.2f€", operationRepository.avgMontant()));
        System.out.println("   - Clients uniques: " + operationRepository.countDistinctClients());
    }
}
