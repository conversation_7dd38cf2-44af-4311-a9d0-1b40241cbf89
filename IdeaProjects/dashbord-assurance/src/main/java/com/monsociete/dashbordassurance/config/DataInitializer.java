package com.monsociete.dashbordassurance.config;

import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.model.Contrat;
import com.monsociete.dashbordassurance.model.Utilisateur;
import com.monsociete.dashbordassurance.repository.OperationRepository;
import com.monsociete.dashbordassurance.repository.ContratRepository;
import com.monsociete.dashbordassurance.repository.UtilisateurRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private OperationRepository operationRepository;

    @Autowired
    private ContratRepository contratRepository;

    @Autowired
    private UtilisateurRepository utilisateurRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // Vérifier si des données existent déjà
        if (utilisateurRepository.count() == 0) {
            initializeTestData();
        }
    }

    private void initializeTestData() {
        Random random = new Random();

        // 1. Créer des utilisateurs de test
        List<Utilisateur> utilisateurs = createTestUsers();

        // 2. Créer des contrats pour ces utilisateurs
        List<Contrat> contrats = createTestContracts(utilisateurs, random);

        // 3. Créer des opérations pour ces contrats
        createTestOperations(contrats, random);

        System.out.println("Données de test initialisées :");
        System.out.println("- " + utilisateurRepository.count() + " utilisateurs créés");
        System.out.println("- " + contratRepository.count() + " contrats créés");
        System.out.println("- " + operationRepository.count() + " opérations créées");
    }

    private List<Utilisateur> createTestUsers() {
        List<Utilisateur> utilisateurs = Arrays.asList(
            new Utilisateur("Dupont", "Jean", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Martin", "Marie", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Bernard", "Pierre", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Dubois", "Sophie", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Thomas", "Paul", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Robert", "Julie", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Petit", "Michel", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Richard", "Anne", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Durand", "François", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),
            new Utilisateur("Moreau", "Catherine", "<EMAIL>",
                           passwordEncoder.encode("password123"), Utilisateur.Role.CLIENT),

            // Utilisateurs avec des rôles spéciaux
            new Utilisateur("Admin", "Super", "<EMAIL>",
                           passwordEncoder.encode("admin123"), Utilisateur.Role.ADMIN),
            new Utilisateur("Gestionnaire", "Principal", "<EMAIL>",
                           passwordEncoder.encode("gestionnaire123"), Utilisateur.Role.GESTIONNAIRE),
            new Utilisateur("Conseiller", "Expert", "<EMAIL>",
                           passwordEncoder.encode("conseiller123"), Utilisateur.Role.CONSEILLER)
        );

        // Ajouter des informations supplémentaires
        for (int i = 0; i < utilisateurs.size() - 3; i++) { // Exclure les 3 derniers (admin, gestionnaire, conseiller)
            Utilisateur user = utilisateurs.get(i);
            user.setTelephone("0" + (600000000 + i * 1000000 + new Random().nextInt(999999)));
            user.setAdresse((i + 1) + " Rue de la Paix, 75001 Paris");
        }

        return utilisateurRepository.saveAll(utilisateurs);
    }

    private List<Contrat> createTestContracts(List<Utilisateur> utilisateurs, Random random) {
        List<Contrat> contrats = Arrays.asList(
            // Contrats pour Jean Dupont
            new Contrat("AV001", "Assurance Vie Premium", Contrat.TypeProduit.ASSURANCE_VIE,
                       new BigDecimal("50000"), utilisateurs.get(0)),
            new Contrat("PEA001", "PEA Dynamique", Contrat.TypeProduit.PEA,
                       new BigDecimal("25000"), utilisateurs.get(0)),

            // Contrats pour Marie Martin
            new Contrat("AV002", "Assurance Vie Classique", Contrat.TypeProduit.ASSURANCE_VIE,
                       new BigDecimal("75000"), utilisateurs.get(1)),
            new Contrat("PERP001", "PERP Sécurisé", Contrat.TypeProduit.PERP,
                       new BigDecimal("30000"), utilisateurs.get(1)),

            // Contrats pour Pierre Bernard
            new Contrat("MS001", "Multisupport Équilibré", Contrat.TypeProduit.MULTISUPPORT,
                       new BigDecimal("40000"), utilisateurs.get(2)),
            new Contrat("CC001", "Contrat Capitalisation", Contrat.TypeProduit.CONTRAT_CAPITALISATION,
                       new BigDecimal("60000"), utilisateurs.get(2)),

            // Contrats pour Sophie Dubois
            new Contrat("AV003", "Assurance Vie Jeune", Contrat.TypeProduit.ASSURANCE_VIE,
                       new BigDecimal("20000"), utilisateurs.get(3)),
            new Contrat("PEA002", "PEA Prudent", Contrat.TypeProduit.PEA,
                       new BigDecimal("15000"), utilisateurs.get(3)),

            // Contrats pour Paul Thomas
            new Contrat("PER001", "Plan Épargne Retraite", Contrat.TypeProduit.PER,
                       new BigDecimal("80000"), utilisateurs.get(4)),
            new Contrat("AV004", "Assurance Vie Famille", Contrat.TypeProduit.ASSURANCE_VIE,
                       new BigDecimal("45000"), utilisateurs.get(4)),

            // Contrats pour Julie Robert
            new Contrat("MS002", "Multisupport Offensif", Contrat.TypeProduit.MULTISUPPORT,
                       new BigDecimal("35000"), utilisateurs.get(5)),

            // Contrats pour Michel Petit
            new Contrat("CC002", "Capitalisation Senior", Contrat.TypeProduit.CONTRAT_CAPITALISATION,
                       new BigDecimal("90000"), utilisateurs.get(6)),
            new Contrat("PERP002", "PERP Complémentaire", Contrat.TypeProduit.PERP,
                       new BigDecimal("25000"), utilisateurs.get(6)),

            // Contrats pour Anne Richard
            new Contrat("AV005", "Assurance Vie Flexible", Contrat.TypeProduit.ASSURANCE_VIE,
                       new BigDecimal("55000"), utilisateurs.get(7)),

            // Contrats pour François Durand
            new Contrat("PEA003", "PEA Croissance", Contrat.TypeProduit.PEA,
                       new BigDecimal("40000"), utilisateurs.get(8)),
            new Contrat("MS003", "Multisupport Défensif", Contrat.TypeProduit.MULTISUPPORT,
                       new BigDecimal("30000"), utilisateurs.get(8)),

            // Contrats pour Catherine Moreau
            new Contrat("AV006", "Assurance Vie Retraite", Contrat.TypeProduit.ASSURANCE_VIE,
                       new BigDecimal("70000"), utilisateurs.get(9)),
            new Contrat("PER002", "PER Optimisé", Contrat.TypeProduit.PER,
                       new BigDecimal("50000"), utilisateurs.get(9))
        );

        // Définir des dates d'ouverture aléatoires dans les 2 dernières années
        for (Contrat contrat : contrats) {
            LocalDate dateOuverture = LocalDate.now().minusDays(random.nextInt(730)); // 2 ans
            contrat.setDateOuverture(dateOuverture);

            // Définir un taux de rendement aléatoire
            contrat.setTauxRendement(new BigDecimal(1.0 + random.nextDouble() * 4.0)); // Entre 1% et 5%

            // Ajuster le montant actuel (peut avoir évolué)
            double evolution = 0.8 + random.nextDouble() * 0.4; // Entre 80% et 120% du montant initial
            contrat.setMontantActuel(contrat.getMontantInitial().multiply(new BigDecimal(evolution)));
        }

        return contratRepository.saveAll(contrats);
    }

    private void createTestOperations(List<Contrat> contrats, Random random) {
        List<Operation.TypeOperation> types = Arrays.asList(
            Operation.TypeOperation.VERSEMENT,
            Operation.TypeOperation.RACHAT,
            Operation.TypeOperation.ARBITRAGE,
            Operation.TypeOperation.AVANCE,
            Operation.TypeOperation.TRANSFERT
        );

        List<Operation.CanalOperation> canaux = Arrays.asList(
            Operation.CanalOperation.AGENCE,
            Operation.CanalOperation.INTERNET,
            Operation.CanalOperation.TELEPHONE,
            Operation.CanalOperation.MOBILE
        );

        List<Operation.StatutOperation> statuts = Arrays.asList(
            Operation.StatutOperation.VALIDEE,
            Operation.StatutOperation.VALIDEE,
            Operation.StatutOperation.VALIDEE,
            Operation.StatutOperation.EN_COURS,
            Operation.StatutOperation.EN_ATTENTE
        );

        // Générer 150 opérations de test
        for (int i = 0; i < 150; i++) {
            Operation operation = new Operation();

            // Sélectionner un contrat aléatoire
            Contrat contrat = contrats.get(random.nextInt(contrats.size()));
            operation.setContrat(contrat);

            // Type d'opération aléatoire
            operation.setType(types.get(random.nextInt(types.size())));

            // Montant aléatoire selon le type
            BigDecimal montant;
            if (operation.getType() == Operation.TypeOperation.VERSEMENT) {
                montant = new BigDecimal(1000 + random.nextDouble() * 20000); // 1K à 21K
            } else if (operation.getType() == Operation.TypeOperation.RACHAT) {
                montant = new BigDecimal(500 + random.nextDouble() * 15000); // 500 à 15.5K
            } else {
                montant = new BigDecimal(100 + random.nextDouble() * 5000); // 100 à 5.1K
            }
            operation.setMontant(montant);

            // Date aléatoire dans les 6 derniers mois
            LocalDate dateOperation = LocalDate.now().minusDays(random.nextInt(180));
            operation.setDate(dateOperation);

            // Canal et statut aléatoires
            operation.setCanal(canaux.get(random.nextInt(canaux.size())));
            operation.setStatut(statuts.get(random.nextInt(statuts.size())));

            // Référence externe
            operation.setReferenceExterne("REF" + String.format("%06d", i + 1));

            // Commentaire occasionnel
            if (random.nextDouble() < 0.3) { // 30% de chance d'avoir un commentaire
                operation.setCommentaire("Opération " + operation.getType().getLibelle().toLowerCase() +
                                       " effectuée via " + operation.getCanal().getLibelle().toLowerCase());
            }

            operationRepository.save(operation);
        }
    }
}
