package com.monsociete.dashbordassurance.config;

import com.monsociete.dashbordassurance.entity.Operation;
import com.monsociete.dashbordassurance.repository.OperationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Random;

@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private OperationRepository operationRepository;
    
    @Override
    public void run(String... args) throws Exception {
        // Vérifier si des données existent déjà
        if (operationRepository.count() > 0) {
            System.out.println("✅ Données déjà présentes dans la base de données (" + operationRepository.count() + " opérations)");
            return;
        }
        
        System.out.println("🔄 Initialisation des données de test...");
        
        String[] types = {"VERSEMENT", "RACHAT", "AR<PERSON><PERSON>AG<PERSON>", "AVANC<PERSON>", "TRANSFERT"};
        String[] clients = {
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON><PERSON>", "<PERSON> Moreau",
            "<PERSON> Rousseau", "Sylvie Leroy", "Patrick Moreau"
        };
        String[] produits = {
            "Assurance Vie Premium", "PEA Dynamique", "PERP Sécurisé", 
            "Multisupport Équilibré", "Contrat Capitalisation"
        };
        String[] statuts = {"VALIDEE", "EN_COURS", "EN_ATTENTE", "REJETEE"};
        String[] canaux = {"AGENCE", "INTERNET", "TELEPHONE", "MOBILE"};
        
        Random random = new Random(42); // Seed fixe pour des données reproductibles
        
        for (int i = 1; i <= 150; i++) {
            Operation operation = new Operation();
            
            operation.setType(types[random.nextInt(types.length)]);
            operation.setClientNom(clients[random.nextInt(clients.length)]);
            operation.setProduitNom(produits[random.nextInt(produits.length)]);
            operation.setMontant((double) (1000 + random.nextInt(49000))); // Entre 1000 et 50000
            
            // Date aléatoire dans les 6 derniers mois
            LocalDate baseDate = LocalDate.now().minusDays(180);
            LocalDate randomDate = baseDate.plusDays(random.nextInt(180));
            operation.setDate(randomDate);
            
            operation.setStatut(statuts[random.nextInt(statuts.length)]);
            operation.setCanal(canaux[random.nextInt(canaux.length)]);
            operation.setCommentaire("Opération " + operation.getType() + " via " + operation.getCanal());
            operation.setReferenceExterne("REF" + String.format("%06d", i));
            
            operationRepository.save(operation);
        }
        
        System.out.println("✅ " + operationRepository.count() + " opérations créées avec succès !");
        
        // Afficher quelques statistiques
        System.out.println("📊 Statistiques:");
        System.out.println("   - Total opérations: " + operationRepository.countTotalOperations());
        System.out.println("   - Montant total: " + String.format("%.2f€", operationRepository.sumTotalMontant()));
        System.out.println("   - Montant moyen: " + String.format("%.2f€", operationRepository.avgMontant()));
        System.out.println("   - Clients uniques: " + operationRepository.countDistinctClients());
    }
}
