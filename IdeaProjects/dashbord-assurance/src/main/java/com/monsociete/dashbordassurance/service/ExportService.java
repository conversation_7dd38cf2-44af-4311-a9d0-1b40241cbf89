package com.monsociete.dashbordassurance.service;

import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.model.Contrat;
import com.monsociete.dashbordassurance.model.Utilisateur;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ExportService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    // Export des opérations en Excel
    public byte[] exportOperationsToExcel(List<Operation> operations) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Opérations");

            // Style pour l'en-tête
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // Créer l'en-tête
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ID", "Type", "Client", "Produit", "Montant", "Date", "Statut", "Canal"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Remplir les données
            int rowNum = 1;
            for (Operation operation : operations) {
                Row row = sheet.createRow(rowNum++);
                
                row.createCell(0).setCellValue(operation.getId());
                row.createCell(1).setCellValue(operation.getType().getLibelle());
                row.createCell(2).setCellValue(operation.getClientNom());
                row.createCell(3).setCellValue(operation.getProduitNom());
                row.createCell(4).setCellValue(operation.getMontant().doubleValue());
                row.createCell(5).setCellValue(operation.getDate().format(DATE_FORMATTER));
                row.createCell(6).setCellValue(operation.getStatut().getLibelle());
                row.createCell(7).setCellValue(operation.getCanal().getLibelle());
            }

            // Ajuster la largeur des colonnes
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Convertir en byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    // Export des contrats en Excel
    public byte[] exportContratsToExcel(List<Contrat> contrats) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Contrats");

            // Style pour l'en-tête
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.DARK_GREEN.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // Créer l'en-tête
            Row headerRow = sheet.createRow(0);
            String[] headers = {"ID", "Numéro", "Client", "Produit", "Type", "Montant Initial", 
                               "Montant Actuel", "Date Ouverture", "Statut"};
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Remplir les données
            int rowNum = 1;
            for (Contrat contrat : contrats) {
                Row row = sheet.createRow(rowNum++);
                
                row.createCell(0).setCellValue(contrat.getId());
                row.createCell(1).setCellValue(contrat.getNumeroContrat());
                row.createCell(2).setCellValue(contrat.getUtilisateur().getNomComplet());
                row.createCell(3).setCellValue(contrat.getProduit());
                row.createCell(4).setCellValue(contrat.getTypeProduit().getLibelle());
                row.createCell(5).setCellValue(contrat.getMontantInitial() != null ? 
                                              contrat.getMontantInitial().doubleValue() : 0);
                row.createCell(6).setCellValue(contrat.getMontantActuel() != null ? 
                                              contrat.getMontantActuel().doubleValue() : 0);
                row.createCell(7).setCellValue(contrat.getDateOuverture().format(DATE_FORMATTER));
                row.createCell(8).setCellValue(contrat.getStatut().name());
            }

            // Ajuster la largeur des colonnes
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Convertir en byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    // Export des opérations en PDF
    public byte[] exportOperationsToPdf(List<Operation> operations, String titre) throws IOException {
        StringBuilder html = new StringBuilder();
        
        // En-tête HTML
        html.append("<!DOCTYPE html>")
            .append("<html><head>")
            .append("<meta charset='UTF-8'>")
            .append("<style>")
            .append("body { font-family: Arial, sans-serif; margin: 20px; }")
            .append("h1 { color: #2c3e50; text-align: center; }")
            .append("table { width: 100%; border-collapse: collapse; margin-top: 20px; }")
            .append("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }")
            .append("th { background-color: #3498db; color: white; }")
            .append("tr:nth-child(even) { background-color: #f2f2f2; }")
            .append(".montant { text-align: right; }")
            .append(".date { text-align: center; }")
            .append("</style>")
            .append("</head><body>");

        // Titre
        html.append("<h1>").append(titre != null ? titre : "Rapport des Opérations").append("</h1>");

        // Tableau
        html.append("<table>")
            .append("<thead>")
            .append("<tr>")
            .append("<th>ID</th>")
            .append("<th>Type</th>")
            .append("<th>Client</th>")
            .append("<th>Produit</th>")
            .append("<th>Montant</th>")
            .append("<th>Date</th>")
            .append("<th>Statut</th>")
            .append("</tr>")
            .append("</thead>")
            .append("<tbody>");

        // Données
        for (Operation operation : operations) {
            html.append("<tr>")
                .append("<td>").append(operation.getId()).append("</td>")
                .append("<td>").append(operation.getType().getLibelle()).append("</td>")
                .append("<td>").append(operation.getClientNom()).append("</td>")
                .append("<td>").append(operation.getProduitNom()).append("</td>")
                .append("<td class='montant'>").append(String.format("%.2f €", operation.getMontant())).append("</td>")
                .append("<td class='date'>").append(operation.getDate().format(DATE_FORMATTER)).append("</td>")
                .append("<td>").append(operation.getStatut().getLibelle()).append("</td>")
                .append("</tr>");
        }

        html.append("</tbody></table>");

        // Pied de page
        html.append("<p style='margin-top: 30px; text-align: center; color: #7f8c8d;'>")
            .append("Rapport généré le ").append(java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy à HH:mm")))
            .append("</p>");

        html.append("</body></html>");

        // Convertir HTML en PDF
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HtmlConverter.convertToPdf(html.toString(), outputStream);
        return outputStream.toByteArray();
    }

    // Export d'un rapport de synthèse pour un client
    public byte[] exportRapportClientToPdf(Utilisateur client, List<Contrat> contrats, 
                                          List<Operation> operations) throws IOException {
        StringBuilder html = new StringBuilder();
        
        // En-tête HTML avec styles améliorés
        html.append("<!DOCTYPE html>")
            .append("<html><head>")
            .append("<meta charset='UTF-8'>")
            .append("<style>")
            .append("body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }")
            .append("h1 { color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }")
            .append("h2 { color: #34495e; margin-top: 30px; }")
            .append(".client-info { background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }")
            .append("table { width: 100%; border-collapse: collapse; margin-top: 15px; }")
            .append("th, td { border: 1px solid #bdc3c7; padding: 10px; text-align: left; }")
            .append("th { background-color: #3498db; color: white; }")
            .append("tr:nth-child(even) { background-color: #f8f9fa; }")
            .append(".montant { text-align: right; font-weight: bold; }")
            .append(".date { text-align: center; }")
            .append(".summary { background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }")
            .append("</style>")
            .append("</head><body>");

        // Titre
        html.append("<h1>Rapport de Gestion Client</h1>");

        // Informations client
        html.append("<div class='client-info'>")
            .append("<h2>Informations Client</h2>")
            .append("<p><strong>Nom :</strong> ").append(client.getNomComplet()).append("</p>")
            .append("<p><strong>Email :</strong> ").append(client.getEmail()).append("</p>");
        if (client.getTelephone() != null) {
            html.append("<p><strong>Téléphone :</strong> ").append(client.getTelephone()).append("</p>");
        }
        html.append("</div>");

        // Résumé des contrats
        html.append("<h2>Contrats</h2>");
        if (!contrats.isEmpty()) {
            html.append("<table>")
                .append("<thead><tr>")
                .append("<th>Numéro</th><th>Produit</th><th>Type</th>")
                .append("<th>Montant Actuel</th><th>Date Ouverture</th><th>Statut</th>")
                .append("</tr></thead><tbody>");

            double totalMontant = 0;
            for (Contrat contrat : contrats) {
                html.append("<tr>")
                    .append("<td>").append(contrat.getNumeroContrat()).append("</td>")
                    .append("<td>").append(contrat.getProduit()).append("</td>")
                    .append("<td>").append(contrat.getTypeProduit().getLibelle()).append("</td>")
                    .append("<td class='montant'>").append(String.format("%.2f €", contrat.getMontantActuel())).append("</td>")
                    .append("<td class='date'>").append(contrat.getDateOuverture().format(DATE_FORMATTER)).append("</td>")
                    .append("<td>").append(contrat.getStatut().name()).append("</td>")
                    .append("</tr>");
                totalMontant += contrat.getMontantActuel().doubleValue();
            }
            html.append("</tbody></table>");

            html.append("<div class='summary'>")
                .append("<p><strong>Total des montants :</strong> ").append(String.format("%.2f €", totalMontant)).append("</p>")
                .append("<p><strong>Nombre de contrats :</strong> ").append(contrats.size()).append("</p>")
                .append("</div>");
        } else {
            html.append("<p>Aucun contrat trouvé.</p>");
        }

        // Opérations récentes
        html.append("<h2>Opérations Récentes</h2>");
        if (!operations.isEmpty()) {
            html.append("<table>")
                .append("<thead><tr>")
                .append("<th>Type</th><th>Montant</th><th>Date</th><th>Statut</th>")
                .append("</tr></thead><tbody>");

            for (Operation operation : operations) {
                html.append("<tr>")
                    .append("<td>").append(operation.getType().getLibelle()).append("</td>")
                    .append("<td class='montant'>").append(String.format("%.2f €", operation.getMontant())).append("</td>")
                    .append("<td class='date'>").append(operation.getDate().format(DATE_FORMATTER)).append("</td>")
                    .append("<td>").append(operation.getStatut().getLibelle()).append("</td>")
                    .append("</tr>");
            }
            html.append("</tbody></table>");
        } else {
            html.append("<p>Aucune opération trouvée.</p>");
        }

        // Pied de page
        html.append("<p style='margin-top: 40px; text-align: center; color: #7f8c8d; border-top: 1px solid #bdc3c7; padding-top: 20px;'>")
            .append("Rapport généré le ").append(java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy à HH:mm")))
            .append("<br>Dashboard Assurance - Gestion des opérations")
            .append("</p>");

        html.append("</body></html>");

        // Convertir HTML en PDF
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        HtmlConverter.convertToPdf(html.toString(), outputStream);
        return outputStream.toByteArray();
    }
}
