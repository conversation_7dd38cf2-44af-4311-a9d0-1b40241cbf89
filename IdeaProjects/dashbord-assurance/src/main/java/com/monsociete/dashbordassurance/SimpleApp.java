package com.monsociete.dashbordassurance;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {"com.monsociete.dashbordassurance.controllers"})
public class SimpleApp {

    public static void main(String[] args) {
        System.out.println("🚀 Démarrage de l'application Dashboard Assurance Simple");
        SpringApplication.run(SimpleApp.class, args);
        System.out.println("✅ Application démarrée avec succès sur http://localhost:8081");
        System.out.println("📋 API disponible sur http://localhost:8081/api");
        System.out.println("🔐 Comptes de test :");
        System.out.println("   - <EMAIL> / admin123");
        System.out.println("   - <EMAIL> / gestionnaire123");
        System.out.println("   - <EMAIL> / conseiller123");
        System.out.println("   - <EMAIL> / client123");
    }
}
