package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.time.LocalDate;

@RestController
@RequestMapping("/api/contrats")
@CrossOrigin(origins = "*")
public class ContratController {

    private static final List<Map<String, Object>> CONTRATS_DATA = generateContratsData();

    @GetMapping
    public List<Map<String, Object>> getAllContrats() {
        return CONTRATS_DATA;
    }

    @GetMapping("/{id}")
    public Map<String, Object> getContratById(@PathVariable Long id) {
        return CONTRATS_DATA.stream()
                .filter(contrat -> contrat.get("id").equals(id))
                .findFirst()
                .orElse(null);
    }

    @GetMapping("/client/{clientNom}")
    public List<Map<String, Object>> getContratsByClient(@PathVariable String clientNom) {
        return CONTRATS_DATA.stream()
                .filter(contrat -> contrat.get("clientNom").toString().equals(clientNom))
                .toList();
    }

    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Statistiques générales
        stats.put("totalContrats", CONTRATS_DATA.size());
        stats.put("contratsActifs", CONTRATS_DATA.stream().filter(c -> "ACTIF".equals(c.get("statut"))).count());
        stats.put("montantTotal", CONTRATS_DATA.stream().mapToDouble(c -> (Double) c.get("montantActuel")).sum());
        
        // Statistiques par type de produit
        Map<String, Object> statsByType = new HashMap<>();
        CONTRATS_DATA.stream()
                .collect(java.util.stream.Collectors.groupingBy(c -> c.get("typeProduit").toString()))
                .forEach((type, contrats) -> {
                    Map<String, Object> typeStats = new HashMap<>();
                    typeStats.put("count", contrats.size());
                    typeStats.put("montantTotal", contrats.stream().mapToDouble(c -> (Double) c.get("montantActuel")).sum());
                    statsByType.put(type, typeStats);
                });
        stats.put("statsByType", statsByType);
        
        return stats;
    }

    private static List<Map<String, Object>> generateContratsData() {
        List<Map<String, Object>> contrats = new ArrayList<>();
        
        String[] clients = {
            "Jean Dupont", "Marie Martin", "Pierre Bernard", "Sophie Dubois", "Paul Thomas",
            "Julie Robert", "Michel Petit", "Anne Richard", "François Durand", "Catherine Moreau"
        };
        
        String[] typesProduits = {"ASSURANCE_VIE", "PEA", "PERP", "MULTISUPPORT", "CONTRAT_CAPITALISATION"};
        String[] produits = {
            "Assurance Vie Premium", "PEA Dynamique", "PERP Sécurisé", 
            "Multisupport Équilibré", "Contrat Capitalisation"
        };
        String[] statuts = {"ACTIF", "SUSPENDU", "CLOTURE"};
        
        Random random = new Random(42);
        
        for (int i = 1; i <= 25; i++) {
            Map<String, Object> contrat = new HashMap<>();
            contrat.put("id", (long) i);
            contrat.put("numeroContrat", "CT" + String.format("%06d", i));
            contrat.put("clientNom", clients[random.nextInt(clients.length)]);
            contrat.put("produit", produits[random.nextInt(produits.length)]);
            contrat.put("typeProduit", typesProduits[random.nextInt(typesProduits.length)]);
            
            double montantInitial = 10000 + random.nextInt(90000); // Entre 10K et 100K
            contrat.put("montantInitial", montantInitial);
            contrat.put("montantActuel", montantInitial * (0.8 + random.nextDouble() * 0.4)); // ±20%
            
            LocalDate dateOuverture = LocalDate.now().minusDays(random.nextInt(1095)); // 3 ans max
            contrat.put("dateOuverture", dateOuverture.toString());
            
            contrat.put("statut", statuts[random.nextInt(statuts.length)]);
            contrat.put("tauxRendement", 1.0 + random.nextDouble() * 4.0); // Entre 1% et 5%
            
            contrats.add(contrat);
        }
        
        return contrats;
    }
}
