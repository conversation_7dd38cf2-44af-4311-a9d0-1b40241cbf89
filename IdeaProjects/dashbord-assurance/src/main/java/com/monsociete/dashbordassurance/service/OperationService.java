package com.monsociete.dashbordassurance.service;

import com.monsociete.dashbordassurance.entity.Operation;
import com.monsociete.dashbordassurance.repository.OperationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
public class OperationService {
    
    @Autowired
    private OperationRepository operationRepository;
    
    public List<Operation> getAllOperations() {
        return operationRepository.findAll();
    }
    
    public Optional<Operation> getOperationById(Long id) {
        return operationRepository.findById(id);
    }
    
    public List<Operation> getOperationsWithFilters(String type, String clientNom, String produitNom, 
                                                   LocalDate dateDebut, LocalDate dateFin) {
        return operationRepository.findWithFilters(type, clientNom, produitNom, dateDebut, dateFin);
    }
    
    public Operation saveOperation(Operation operation) {
        return operationRepository.save(operation);
    }
    
    public void deleteOperation(Long id) {
        operationRepository.deleteById(id);
    }
    
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // KPIs généraux
        stats.put("totalOperations", operationRepository.countTotalOperations());
        stats.put("totalMontant", operationRepository.sumTotalMontant());
        stats.put("montantMoyen", operationRepository.avgMontant());
        stats.put("clientsUniques", operationRepository.countDistinctClients());
        
        // Statistiques par type
        Map<String, Map<String, Object>> statsByType = new HashMap<>();
        List<Object[]> typeStats = operationRepository.findStatsByType();
        for (Object[] stat : typeStats) {
            String type = (String) stat[0];
            Long count = (Long) stat[1];
            Double montant = (Double) stat[2];
            
            Map<String, Object> typeData = new HashMap<>();
            typeData.put("count", count);
            typeData.put("montant", montant);
            statsByType.put(type, typeData);
        }
        stats.put("statsByType", statsByType);
        
        // Top clients
        Map<String, Map<String, Object>> statsByClient = new HashMap<>();
        List<Object[]> clientStats = operationRepository.findTopClientsByMontant();
        int clientCount = 0;
        for (Object[] stat : clientStats) {
            if (clientCount >= 10) break; // Top 10 seulement
            
            String client = (String) stat[0];
            Double montant = (Double) stat[1];
            
            // Compter les opérations pour ce client
            Long count = (long) operationRepository.findByClientNomContainingIgnoreCase(client).size();
            
            Map<String, Object> clientData = new HashMap<>();
            clientData.put("count", count);
            clientData.put("montant", montant);
            statsByClient.put(client, clientData);
            clientCount++;
        }
        stats.put("statsByClient", statsByClient);
        
        // Évolution temporelle
        Map<String, Map<String, Object>> evolution = new HashMap<>();
        List<Object[]> evolutionStats = operationRepository.findEvolutionByMonth();
        for (Object[] stat : evolutionStats) {
            Integer year = (Integer) stat[0];
            Integer month = (Integer) stat[1];
            Long count = (Long) stat[2];
            Double montant = (Double) stat[3];
            
            String monthKey = String.format("%04d-%02d", year, month);
            Map<String, Object> monthData = new HashMap<>();
            monthData.put("count", count);
            monthData.put("montant", montant);
            evolution.put(monthKey, monthData);
        }
        stats.put("evolution", evolution);
        
        return stats;
    }
}
