package com.monsociete.dashbordassurance.repository;

import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.model.Contrat;
import com.monsociete.dashbordassurance.model.Utilisateur;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface OperationRepository extends JpaRepository<Operation, Long> {

    // Recherche par type d'opération
    List<Operation> findByType(Operation.TypeOperation type);

    // Recherche par contrat
    List<Operation> findByContrat(Contrat contrat);

    // Recherche par statut
    List<Operation> findByStatut(Operation.StatutOperation statut);

    // Recherche par canal
    List<Operation> findByCanal(Operation.CanalOperation canal);

    // Recherche par période
    List<Operation> findByDateBetween(LocalDate startDate, LocalDate endDate);

    // Recherche par utilisateur (via contrat)
    @Query("SELECT o FROM Operation o WHERE o.contrat.utilisateur = :utilisateur")
    List<Operation> findByUtilisateur(@Param("utilisateur") Utilisateur utilisateur);

    // Recherche par email utilisateur
    @Query("SELECT o FROM Operation o WHERE o.contrat.utilisateur.email = :email")
    List<Operation> findByUtilisateurEmail(@Param("email") String email);

    // Requête personnalisée pour filtrer avec les nouvelles relations
    @Query("SELECT o FROM Operation o WHERE " +
            "(:type IS NULL OR o.type = :type) AND " +
            "(:utilisateurId IS NULL OR o.contrat.utilisateur.id = :utilisateurId) AND " +
            "(:contratId IS NULL OR o.contrat.id = :contratId) AND " +
            "(:typeProduit IS NULL OR o.contrat.typeProduit = :typeProduit) AND " +
            "(:statut IS NULL OR o.statut = :statut) AND " +
            "(:canal IS NULL OR o.canal = :canal) AND " +
            "(:dateDebut IS NULL OR o.date >= :dateDebut) AND " +
            "(:dateFin IS NULL OR o.date <= :dateFin) AND " +
            "(:montantMin IS NULL OR o.montant >= :montantMin) AND " +
            "(:montantMax IS NULL OR o.montant <= :montantMax)")
    List<Operation> findOperationsWithFilters(
            @Param("type") Operation.TypeOperation type,
            @Param("utilisateurId") Long utilisateurId,
            @Param("contratId") Long contratId,
            @Param("typeProduit") Contrat.TypeProduit typeProduit,
            @Param("statut") Operation.StatutOperation statut,
            @Param("canal") Operation.CanalOperation canal,
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin,
            @Param("montantMin") BigDecimal montantMin,
            @Param("montantMax") BigDecimal montantMax
    );

    // Statistiques par type d'opération
    @Query("SELECT o.type, COUNT(o), SUM(o.montant) FROM Operation o " +
           "WHERE (:dateDebut IS NULL OR o.date >= :dateDebut) AND " +
           "(:dateFin IS NULL OR o.date <= :dateFin) " +
           "GROUP BY o.type")
    List<Object[]> getStatistiquesByType(
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin
    );

    // Statistiques par utilisateur
    @Query("SELECT o.contrat.utilisateur, COUNT(o), SUM(o.montant) FROM Operation o " +
           "WHERE (:dateDebut IS NULL OR o.date >= :dateDebut) AND " +
           "(:dateFin IS NULL OR o.date <= :dateFin) " +
           "GROUP BY o.contrat.utilisateur " +
           "ORDER BY SUM(o.montant) DESC")
    List<Object[]> getStatistiquesByUtilisateur(
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin
    );

    // Statistiques par produit
    @Query("SELECT o.contrat.typeProduit, COUNT(o), SUM(o.montant) FROM Operation o " +
           "WHERE (:dateDebut IS NULL OR o.date >= :dateDebut) AND " +
           "(:dateFin IS NULL OR o.date <= :dateFin) " +
           "GROUP BY o.contrat.typeProduit")
    List<Object[]> getStatistiquesByProduit(
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin
    );

    // Évolution temporelle
    @Query("SELECT DATE(o.date), COUNT(o), SUM(o.montant) FROM Operation o " +
           "WHERE (:dateDebut IS NULL OR o.date >= :dateDebut) AND " +
           "(:dateFin IS NULL OR o.date <= :dateFin) " +
           "GROUP BY DATE(o.date) " +
           "ORDER BY DATE(o.date)")
    List<Object[]> getEvolutionTemporelle(
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin
    );

    // Opérations récentes
    @Query("SELECT o FROM Operation o " +
           "ORDER BY o.dateCreation DESC")
    List<Operation> findRecentOperations();

    // Montant total par période
    @Query("SELECT SUM(o.montant) FROM Operation o " +
           "WHERE o.date BETWEEN :dateDebut AND :dateFin AND o.statut = 'VALIDEE'")
    BigDecimal getTotalMontantByPeriod(
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin
    );
}
