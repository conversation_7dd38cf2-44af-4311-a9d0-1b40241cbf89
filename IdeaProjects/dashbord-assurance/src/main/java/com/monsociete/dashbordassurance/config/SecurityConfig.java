package com.monsociete.dashbordassurance.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // Endpoints publics
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/test").permitAll()
                .requestMatchers("/h2-console/**").permitAll()
                
                // Endpoints pour les clients
                .requestMatchers("/api/operations/**").hasAnyRole("CLIENT", "CONSEILLER", "GESTIONNAIRE", "ADMIN")
                .requestMatchers("/api/contrats/**").hasAnyRole("CLIENT", "CONSEILLER", "GESTIONNAIRE", "ADMIN")
                
                // Endpoints pour les conseillers et plus
                .requestMatchers("/api/utilisateurs/clients").hasAnyRole("CONSEILLER", "GESTIONNAIRE", "ADMIN")
                .requestMatchers("/api/export/**").hasAnyRole("CONSEILLER", "GESTIONNAIRE", "ADMIN")
                
                // Endpoints pour les gestionnaires et plus
                .requestMatchers("/api/utilisateurs/**").hasAnyRole("GESTIONNAIRE", "ADMIN")
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                
                // Tout le reste nécessite une authentification
                .anyRequest().authenticated()
            )
            .headers(headers -> headers.frameOptions().disable()); // Pour H2 Console
        
        return http.build();
    }
    
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
