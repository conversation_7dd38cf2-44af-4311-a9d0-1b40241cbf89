package com.monsociete.dashbordassurance.security;

import com.monsociete.dashbordassurance.model.Utilisateur;
import com.monsociete.dashbordassurance.repository.UtilisateurRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Service
@Transactional
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UtilisateurRepository utilisateurRepository;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        Utilisateur utilisateur = utilisateurRepository.findByEmail(email)
                .orElseThrow(() -> new UsernameNotFoundException("Utilisateur non trouvé avec l'email: " + email));

        if (!utilisateur.getActif()) {
            throw new UsernameNotFoundException("Compte utilisateur désactivé: " + email);
        }

        return new UserPrincipal(utilisateur);
    }

    // Classe interne pour représenter l'utilisateur principal
    public static class UserPrincipal implements UserDetails {
        private final Utilisateur utilisateur;

        public UserPrincipal(Utilisateur utilisateur) {
            this.utilisateur = utilisateur;
        }

        @Override
        public Collection<? extends GrantedAuthority> getAuthorities() {
            List<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new SimpleGrantedAuthority("ROLE_" + utilisateur.getRole().name()));
            return authorities;
        }

        @Override
        public String getPassword() {
            return utilisateur.getMotDePasse();
        }

        @Override
        public String getUsername() {
            return utilisateur.getEmail();
        }

        @Override
        public boolean isAccountNonExpired() {
            return true;
        }

        @Override
        public boolean isAccountNonLocked() {
            return utilisateur.getActif();
        }

        @Override
        public boolean isCredentialsNonExpired() {
            return true;
        }

        @Override
        public boolean isEnabled() {
            return utilisateur.getActif();
        }

        // Méthodes utilitaires pour accéder aux informations de l'utilisateur
        public Long getId() {
            return utilisateur.getId();
        }

        public String getNom() {
            return utilisateur.getNom();
        }

        public String getPrenom() {
            return utilisateur.getPrenom();
        }

        public String getEmail() {
            return utilisateur.getEmail();
        }

        public Utilisateur.Role getRole() {
            return utilisateur.getRole();
        }

        public Utilisateur getUtilisateur() {
            return utilisateur;
        }
    }
}
