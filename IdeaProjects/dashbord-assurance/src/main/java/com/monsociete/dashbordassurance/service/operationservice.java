package com.monsociete.dashbordassurance.service;

import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.model.Contrat;
import com.monsociete.dashbordassurance.repository.OperationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class OperationService {

    @Autowired
    private OperationRepository operationRepository;

    @Autowired
    private ContratService contratService;

    // Récupérer toutes les opérations
    public List<Operation> getAll() {
        return operationRepository.findAll();
    }

    // Récupérer une opération par ID
    public Optional<Operation> getById(Long id) {
        return operationRepository.findById(id);
    }

    // Créer une nouvelle opération
    public Operation create(Operation operation) {
        // Vérifier que le contrat existe
        if (operation.getContrat() != null && operation.getContrat().getId() != null) {
            Contrat contrat = contratService.getContratById(operation.getContrat().getId())
                    .orElseThrow(() -> new RuntimeException("Contrat non trouvé"));
            operation.setContrat(contrat);
        }

        return operationRepository.save(operation);
    }

    // Enregistrer (ajouter ou modifier) une opération - pour compatibilité
    public Operation save(Operation operation) {
        return operationRepository.save(operation);
    }

    // Vérifier si une opération existe par son ID
    public boolean exists(Long id) {
        return operationRepository.existsById(id);
    }

    // Supprimer une opération par son ID
    public void delete(Long id) {
        operationRepository.deleteById(id);
    }

    // Filtrer les opérations selon les critères donnés - méthode de compatibilité
    public List<Operation> filter(String type, String client, String produit, LocalDate start, LocalDate end) {
        // Cette méthode est conservée pour la compatibilité avec l'ancien code
        // mais utilise maintenant les nouvelles méthodes
        Operation.TypeOperation typeOperation = null;
        if (type != null && !type.isEmpty()) {
            try {
                typeOperation = Operation.TypeOperation.valueOf(type.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Ignorer si le type n'est pas valide
            }
        }

        return searchOperations(typeOperation, null, null, null, null, null, start, end, null, null);
    }

    // Rechercher des opérations avec filtres complets
    public List<Operation> searchOperations(Operation.TypeOperation type, Long utilisateurId, Long contratId,
                                          Contrat.TypeProduit typeProduit, Operation.StatutOperation statut,
                                          Operation.CanalOperation canal, LocalDate dateDebut, LocalDate dateFin,
                                          BigDecimal montantMin, BigDecimal montantMax) {
        return operationRepository.findOperationsWithFilters(
                type, utilisateurId, contratId, typeProduit, statut, canal,
                dateDebut, dateFin, montantMin, montantMax
        );
    }

    // Récupérer les opérations par utilisateur
    public List<Operation> getOperationsByUtilisateur(Long utilisateurId) {
        return operationRepository.findOperationsWithFilters(
                null, utilisateurId, null, null, null, null, null, null, null, null
        );
    }

    // Statistiques par type
    public List<Object[]> getStatistiquesByType(LocalDate dateDebut, LocalDate dateFin) {
        return operationRepository.getStatistiquesByType(dateDebut, dateFin);
    }

    // Statistiques par utilisateur
    public List<Object[]> getStatistiquesByUtilisateur(LocalDate dateDebut, LocalDate dateFin) {
        return operationRepository.getStatistiquesByUtilisateur(dateDebut, dateFin);
    }

    // Statistiques par produit
    public List<Object[]> getStatistiquesByProduit(LocalDate dateDebut, LocalDate dateFin) {
        return operationRepository.getStatistiquesByProduit(dateDebut, dateFin);
    }

    // Évolution temporelle
    public List<Object[]> getEvolutionTemporelle(LocalDate dateDebut, LocalDate dateFin) {
        return operationRepository.getEvolutionTemporelle(dateDebut, dateFin);
    }
}
