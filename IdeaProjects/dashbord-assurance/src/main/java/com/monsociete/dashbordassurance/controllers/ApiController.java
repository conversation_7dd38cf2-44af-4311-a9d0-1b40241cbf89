package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class ApiController {

    @GetMapping("/test")
    public Map<String, String> test() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "Backend Spring Boot fonctionne parfaitement !");
        response.put("status", "OK");
        response.put("timestamp", new Date().toString());
        response.put("version", "1.0.0");
        return response;
    }



    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // KPIs
        stats.put("totalOperations", 50);
        stats.put("totalMontant", 1250000);
        stats.put("montantMoyen", 25000);
        stats.put("clientsUniques", 10);
        
        // Statistiques par type
        Map<String, Integer> statsByType = new HashMap<>();
        statsByType.put("VERSEMENT", 20);
        statsByType.put("RACHAT", 15);
        statsByType.put("ARBITRAGE", 10);
        statsByType.put("AVANCE", 3);
        statsByType.put("TRANSFERT", 2);
        stats.put("statsByType", statsByType);
        
        // Évolution mensuelle
        List<Map<String, Object>> evolution = new ArrayList<>();
        String[] mois = {"Jan", "Fév", "Mar", "Avr", "Mai", "Jun"};
        int[] montants = {180000, 220000, 195000, 240000, 210000, 205000};
        
        for (int i = 0; i < mois.length; i++) {
            Map<String, Object> point = new HashMap<>();
            point.put("mois", mois[i]);
            point.put("montant", montants[i]);
            point.put("operations", 8 + (i * 2));
            evolution.add(point);
        }
        stats.put("evolution", evolution);
        
        return stats;
    }

    @GetMapping("/health")
    public Map<String, String> health() {
        Map<String, String> health = new HashMap<>();
        health.put("status", "UP");
        health.put("database", "H2 Connected");
        health.put("java", System.getProperty("java.version"));
        health.put("spring", "3.x");
        return health;
    }
}
