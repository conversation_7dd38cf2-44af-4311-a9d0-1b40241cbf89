package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/operations")
@CrossOrigin(origins = "*")
public class OperationController {

    private static final List<Map<String, Object>> OPERATIONS_DATA = generateOperationsData();

    @GetMapping
    public List<Map<String, Object>> getAllOperations() {
        return OPERATIONS_DATA;
    }

    @GetMapping("/{id}")
    public Map<String, Object> getOperationById(@PathVariable Long id) {
        return OPERATIONS_DATA.stream()
                .filter(op -> op.get("id").equals(id))
                .findFirst()
                .orElse(null);
    }

    @GetMapping("/filter")
    public List<Map<String, Object>> filterOperations(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String client,
            @RequestParam(required = false) String produit,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        return OPERATIONS_DATA.stream()
                .filter(op -> type == null || op.get("type").equals(type))
                .filter(op -> client == null || op.get("clientNom").toString().contains(client))
                .filter(op -> produit == null || op.get("produitNom").toString().contains(produit))
                .filter(op -> {
                    if (dateDebut == null) return true;
                    LocalDate opDate = LocalDate.parse(op.get("date").toString());
                    return !opDate.isBefore(dateDebut);
                })
                .filter(op -> {
                    if (dateFin == null) return true;
                    LocalDate opDate = LocalDate.parse(op.get("date").toString());
                    return !opDate.isAfter(dateFin);
                })
                .toList();
    }

    @GetMapping("/statistics")
    public Map<String, Object> getStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        List<Map<String, Object>> filteredOps = filterOperations(null, null, null, dateDebut, dateFin);
        
        Map<String, Object> stats = new HashMap<>();
        
        // KPIs généraux
        stats.put("totalOperations", filteredOps.size());
        stats.put("totalMontant", filteredOps.stream().mapToDouble(op -> (Double) op.get("montant")).sum());
        stats.put("montantMoyen", filteredOps.stream().mapToDouble(op -> (Double) op.get("montant")).average().orElse(0));
        stats.put("clientsUniques", filteredOps.stream().map(op -> op.get("clientNom")).distinct().count());
        
        // Statistiques par type
        Map<String, Object> statsByType = new HashMap<>();
        filteredOps.stream()
                .collect(java.util.stream.Collectors.groupingBy(op -> op.get("type").toString()))
                .forEach((type, ops) -> {
                    Map<String, Object> typeStats = new HashMap<>();
                    typeStats.put("count", ops.size());
                    typeStats.put("montant", ops.stream().mapToDouble(op -> (Double) op.get("montant")).sum());
                    statsByType.put(type, typeStats);
                });
        stats.put("statsByType", statsByType);
        
        // Statistiques par client (top 10)
        Map<String, Object> statsByClient = new HashMap<>();
        filteredOps.stream()
                .collect(java.util.stream.Collectors.groupingBy(op -> op.get("clientNom").toString()))
                .entrySet().stream()
                .sorted((e1, e2) -> Double.compare(
                    e2.getValue().stream().mapToDouble(op -> (Double) op.get("montant")).sum(),
                    e1.getValue().stream().mapToDouble(op -> (Double) op.get("montant")).sum()))
                .limit(10)
                .forEach(entry -> {
                    Map<String, Object> clientStats = new HashMap<>();
                    clientStats.put("count", entry.getValue().size());
                    clientStats.put("montant", entry.getValue().stream().mapToDouble(op -> (Double) op.get("montant")).sum());
                    statsByClient.put(entry.getKey(), clientStats);
                });
        stats.put("statsByClient", statsByClient);
        
        // Statistiques par produit
        Map<String, Object> statsByProduit = new HashMap<>();
        filteredOps.stream()
                .collect(java.util.stream.Collectors.groupingBy(op -> op.get("produitNom").toString()))
                .forEach((produit, ops) -> {
                    Map<String, Object> produitStats = new HashMap<>();
                    produitStats.put("count", ops.size());
                    produitStats.put("montant", ops.stream().mapToDouble(op -> (Double) op.get("montant")).sum());
                    statsByProduit.put(produit, produitStats);
                });
        stats.put("statsByProduit", statsByProduit);
        
        // Évolution temporelle (par mois)
        Map<String, Object> evolution = new HashMap<>();
        filteredOps.stream()
                .collect(java.util.stream.Collectors.groupingBy(op -> {
                    LocalDate date = LocalDate.parse(op.get("date").toString());
                    return date.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                }))
                .entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    Map<String, Object> monthStats = new HashMap<>();
                    monthStats.put("count", entry.getValue().size());
                    monthStats.put("montant", entry.getValue().stream().mapToDouble(op -> (Double) op.get("montant")).sum());
                    evolution.put(entry.getKey(), monthStats);
                });
        stats.put("evolution", evolution);
        
        return stats;
    }

    @PostMapping
    public Map<String, Object> createOperation(@RequestBody Map<String, Object> operation) {
        operation.put("id", OPERATIONS_DATA.size() + 1L);
        operation.put("dateCreation", LocalDate.now().toString());
        operation.put("statut", "EN_COURS");
        OPERATIONS_DATA.add(operation);
        return operation;
    }

    @PutMapping("/{id}")
    public Map<String, Object> updateOperation(@PathVariable Long id, @RequestBody Map<String, Object> operation) {
        for (int i = 0; i < OPERATIONS_DATA.size(); i++) {
            if (OPERATIONS_DATA.get(i).get("id").equals(id)) {
                operation.put("id", id);
                operation.put("dateModification", LocalDate.now().toString());
                OPERATIONS_DATA.set(i, operation);
                return operation;
            }
        }
        return null;
    }

    @DeleteMapping("/{id}")
    public Map<String, String> deleteOperation(@PathVariable Long id) {
        boolean removed = OPERATIONS_DATA.removeIf(op -> op.get("id").equals(id));
        Map<String, String> response = new HashMap<>();
        response.put("status", removed ? "success" : "not_found");
        response.put("message", removed ? "Opération supprimée" : "Opération non trouvée");
        return response;
    }

    private static List<Map<String, Object>> generateOperationsData() {
        List<Map<String, Object>> operations = new ArrayList<>();
        
        String[] types = {"VERSEMENT", "RACHAT", "ARBITRAGE", "AVANCE", "TRANSFERT"};
        String[] clients = {
            "Jean Dupont", "Marie Martin", "Pierre Bernard", "Sophie Dubois", "Paul Thomas",
            "Julie Robert", "Michel Petit", "Anne Richard", "François Durand", "Catherine Moreau",
            "Alain Rousseau", "Sylvie Leroy", "Patrick Moreau"
        };
        String[] produits = {
            "Assurance Vie Premium", "PEA Dynamique", "PERP Sécurisé", 
            "Multisupport Équilibré", "Contrat Capitalisation"
        };
        String[] statuts = {"VALIDEE", "EN_COURS", "EN_ATTENTE", "REJETEE"};
        String[] canaux = {"AGENCE", "INTERNET", "TELEPHONE", "MOBILE"};
        
        Random random = new Random(42); // Seed fixe pour des données reproductibles
        
        for (int i = 1; i <= 150; i++) {
            Map<String, Object> operation = new HashMap<>();
            operation.put("id", (long) i);
            operation.put("type", types[random.nextInt(types.length)]);
            operation.put("clientNom", clients[random.nextInt(clients.length)]);
            operation.put("produitNom", produits[random.nextInt(produits.length)]);
            operation.put("montant", (double) (1000 + random.nextInt(49000))); // Entre 1000 et 50000
            
            // Date aléatoire dans les 6 derniers mois
            LocalDate baseDate = LocalDate.now().minusDays(180);
            LocalDate randomDate = baseDate.plusDays(random.nextInt(180));
            operation.put("date", randomDate.toString());
            
            operation.put("statut", statuts[random.nextInt(statuts.length)]);
            operation.put("canal", canaux[random.nextInt(canaux.length)]);
            operation.put("commentaire", "Opération " + operation.get("type") + " via " + operation.get("canal"));
            operation.put("referenceExterne", "REF" + String.format("%06d", i));
            
            operations.add(operation);
        }
        
        return operations;
    }
}
