package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.entity.Operation;
import com.monsociete.dashbordassurance.service.OperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import java.util.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.io.ByteArrayOutputStream;
import java.io.PrintWriter;

@RestController
@RequestMapping("/api/operations")
@CrossOrigin(origins = "*")
public class OperationController {

    @Autowired
    private OperationService operationService;

    @GetMapping
    public List<Operation> getAllOperations() {
        return operationService.getAllOperations();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Operation> getOperationById(@PathVariable Long id) {
        Optional<Operation> operation = operationService.getOperationById(id);
        return operation.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }



    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        return operationService.getStatistics();
    }

    @GetMapping("/filter")
    public List<Operation> filterOperations(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String client,
            @RequestParam(required = false) String produit,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {

        return operationService.getOperationsWithFilters(type, client, produit, dateDebut, dateFin);
    }

    @PostMapping
    public Operation createOperation(@RequestBody Operation operation) {
        if (operation.getDate() == null) {
            operation.setDate(LocalDate.now());
        }
        if (operation.getStatut() == null) {
            operation.setStatut("EN_COURS");
        }
        return operationService.saveOperation(operation);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Operation> updateOperation(@PathVariable Long id, @RequestBody Operation operation) {
        Optional<Operation> existingOperation = operationService.getOperationById(id);
        if (existingOperation.isPresent()) {
            operation.setId(id);
            return ResponseEntity.ok(operationService.saveOperation(operation));
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, String>> deleteOperation(@PathVariable Long id) {
        Optional<Operation> operation = operationService.getOperationById(id);
        if (operation.isPresent()) {
            operationService.deleteOperation(id);
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Opération supprimée avec succès");
            return ResponseEntity.ok(response);
        }

        Map<String, String> response = new HashMap<>();
        response.put("status", "not_found");
        response.put("message", "Opération non trouvée");
        return ResponseEntity.notFound().build();
    }

}
