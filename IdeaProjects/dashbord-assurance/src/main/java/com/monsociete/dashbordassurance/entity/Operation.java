package com.monsociete.dashbordassurance.entity;

import jakarta.persistence.*;
import java.time.LocalDate;

@Entity
@Table(name = "operations")
public class Operation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String type;
    
    @Column(name = "client_nom", nullable = false)
    private String clientNom;
    
    @Column(name = "produit_nom", nullable = false)
    private String produitNom;
    
    @Column(nullable = false)
    private Double montant;
    
    @Column(nullable = false)
    private LocalDate date;
    
    @Column(nullable = false)
    private String statut;
    
    @Column(nullable = false)
    private String canal;
    
    @Column
    private String commentaire;
    
    @Column(name = "reference_externe")
    private String referenceExterne;
    
    // Constructeurs
    public Operation() {}
    
    public Operation(String type, String clientNom, String produitNom, Double montant, 
                    LocalDate date, String statut, String canal, String commentaire, String referenceExterne) {
        this.type = type;
        this.clientNom = clientNom;
        this.produitNom = produitNom;
        this.montant = montant;
        this.date = date;
        this.statut = statut;
        this.canal = canal;
        this.commentaire = commentaire;
        this.referenceExterne = referenceExterne;
    }
    
    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public String getClientNom() { return clientNom; }
    public void setClientNom(String clientNom) { this.clientNom = clientNom; }
    
    public String getProduitNom() { return produitNom; }
    public void setProduitNom(String produitNom) { this.produitNom = produitNom; }
    
    public Double getMontant() { return montant; }
    public void setMontant(Double montant) { this.montant = montant; }
    
    public LocalDate getDate() { return date; }
    public void setDate(LocalDate date) { this.date = date; }
    
    public String getStatut() { return statut; }
    public void setStatut(String statut) { this.statut = statut; }
    
    public String getCanal() { return canal; }
    public void setCanal(String canal) { this.canal = canal; }
    
    public String getCommentaire() { return commentaire; }
    public void setCommentaire(String commentaire) { this.commentaire = commentaire; }
    
    public String getReferenceExterne() { return referenceExterne; }
    public void setReferenceExterne(String referenceExterne) { this.referenceExterne = referenceExterne; }
}
