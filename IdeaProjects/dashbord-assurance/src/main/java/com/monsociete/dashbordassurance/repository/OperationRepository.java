package com.monsociete.dashbordassurance.repository;

import com.monsociete.dashbordassurance.entity.Operation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface OperationRepository extends JpaRepository<Operation, Long> {
    
    // Recherche par type
    List<Operation> findByType(String type);
    
    // Recherche par client
    List<Operation> findByClientNomContainingIgnoreCase(String clientNom);
    
    // Recherche par produit
    List<Operation> findByProduitNomContainingIgnoreCase(String produitNom);
    
    // Recherche par statut
    List<Operation> findByStatut(String statut);
    
    // Recherche par canal
    List<Operation> findByCanal(String canal);
    
    // Recherche par période
    List<Operation> findByDateBetween(LocalDate dateDebut, LocalDate dateFin);
    
    // Recherche combinée
    @Query("SELECT o FROM Operation o WHERE " +
           "(:type IS NULL OR o.type = :type) AND " +
           "(:clientNom IS NULL OR LOWER(o.clientNom) LIKE LOWER(CONCAT('%', :clientNom, '%'))) AND " +
           "(:produitNom IS NULL OR LOWER(o.produitNom) LIKE LOWER(CONCAT('%', :produitNom, '%'))) AND " +
           "(:dateDebut IS NULL OR o.date >= :dateDebut) AND " +
           "(:dateFin IS NULL OR o.date <= :dateFin)")
    List<Operation> findWithFilters(@Param("type") String type,
                                   @Param("clientNom") String clientNom,
                                   @Param("produitNom") String produitNom,
                                   @Param("dateDebut") LocalDate dateDebut,
                                   @Param("dateFin") LocalDate dateFin);
    
    // Statistiques
    @Query("SELECT COUNT(o) FROM Operation o")
    Long countTotalOperations();
    
    @Query("SELECT SUM(o.montant) FROM Operation o")
    Double sumTotalMontant();
    
    @Query("SELECT AVG(o.montant) FROM Operation o")
    Double avgMontant();
    
    @Query("SELECT COUNT(DISTINCT o.clientNom) FROM Operation o")
    Long countDistinctClients();
    
    // Top clients par montant
    @Query("SELECT o.clientNom, SUM(o.montant) as total FROM Operation o " +
           "GROUP BY o.clientNom ORDER BY total DESC")
    List<Object[]> findTopClientsByMontant();
    
    // Statistiques par type
    @Query("SELECT o.type, COUNT(o), SUM(o.montant) FROM Operation o GROUP BY o.type")
    List<Object[]> findStatsByType();
    
    // Évolution par mois
    @Query("SELECT FUNCTION('YEAR', o.date), FUNCTION('MONTH', o.date), COUNT(o), SUM(o.montant) " +
           "FROM Operation o GROUP BY FUNCTION('YEAR', o.date), FUNCTION('MONTH', o.date) " +
           "ORDER BY FUNCTION('YEAR', o.date), FUNCTION('MONTH', o.date)")
    List<Object[]> findEvolutionByMonth();
}
