package com.monsociete.dashbordassurance.repository;

import com.monsociete.dashbordassurance.model.Contrat;
import com.monsociete.dashbordassurance.model.Utilisateur;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface ContratRepository extends JpaRepository<Contrat, Long> {

    // Recherche par numéro de contrat
    Optional<Contrat> findByNumeroContrat(String numeroContrat);

    // Vérifier si un numéro de contrat existe
    boolean existsByNumeroContrat(String numeroContrat);

    // Recherche par utilisateur
    List<Contrat> findByUtilisateur(Utilisateur utilisateur);

    // Recherche par utilisateur et statut
    List<Contrat> findByUtilisateurAndStatut(Utilisateur utilisateur, Contrat.StatutContrat statut);

    // Recherche par statut
    List<Contrat> findByStatut(Contrat.StatutContrat statut);

    // Recherche par type de produit
    List<Contrat> findByTypeProduit(Contrat.TypeProduit typeProduit);

    // Recherche des contrats actifs
    List<Contrat> findByStatutAndDateClotureIsNull(Contrat.StatutContrat statut);

    // Recherche par période d'ouverture
    List<Contrat> findByDateOuvertureBetween(LocalDate startDate, LocalDate endDate);

    // Recherche avec filtres multiples
    @Query("SELECT c FROM Contrat c WHERE " +
           "(:utilisateurId IS NULL OR c.utilisateur.id = :utilisateurId) AND " +
           "(:statut IS NULL OR c.statut = :statut) AND " +
           "(:typeProduit IS NULL OR c.typeProduit = :typeProduit) AND " +
           "(:produit IS NULL OR LOWER(c.produit) LIKE LOWER(CONCAT('%', :produit, '%'))) AND " +
           "(:dateOuvertureDebut IS NULL OR c.dateOuverture >= :dateOuvertureDebut) AND " +
           "(:dateOuvertureFin IS NULL OR c.dateOuverture <= :dateOuvertureFin) AND " +
           "(:montantMin IS NULL OR c.montantActuel >= :montantMin) AND " +
           "(:montantMax IS NULL OR c.montantActuel <= :montantMax)")
    List<Contrat> findContratsWithFilters(
            @Param("utilisateurId") Long utilisateurId,
            @Param("statut") Contrat.StatutContrat statut,
            @Param("typeProduit") Contrat.TypeProduit typeProduit,
            @Param("produit") String produit,
            @Param("dateOuvertureDebut") LocalDate dateOuvertureDebut,
            @Param("dateOuvertureFin") LocalDate dateOuvertureFin,
            @Param("montantMin") BigDecimal montantMin,
            @Param("montantMax") BigDecimal montantMax
    );

    // Statistiques par type de produit
    @Query("SELECT c.typeProduit, COUNT(c), SUM(c.montantActuel) FROM Contrat c " +
           "WHERE c.statut = 'ACTIF' GROUP BY c.typeProduit")
    List<Object[]> getStatistiquesByTypeProduit();

    // Contrats avec le plus d'opérations
    @Query("SELECT c, COUNT(o) as nbOperations FROM Contrat c " +
           "LEFT JOIN c.operations o " +
           "WHERE c.statut = 'ACTIF' " +
           "GROUP BY c " +
           "ORDER BY nbOperations DESC")
    List<Object[]> getContratsWithMostOperations();

    // Montant total par utilisateur
    @Query("SELECT c.utilisateur, SUM(c.montantActuel) FROM Contrat c " +
           "WHERE c.statut = 'ACTIF' " +
           "GROUP BY c.utilisateur " +
           "ORDER BY SUM(c.montantActuel) DESC")
    List<Object[]> getMontantTotalByUtilisateur();

    // Contrats proches de l'échéance (exemple: plus de 10 ans)
    @Query("SELECT c FROM Contrat c WHERE " +
           "c.statut = 'ACTIF' AND " +
           "c.dateOuverture <= :dateLimit")
    List<Contrat> findContratsProchesEcheance(@Param("dateLimit") LocalDate dateLimit);

    // Recherche par email de l'utilisateur
    @Query("SELECT c FROM Contrat c WHERE c.utilisateur.email = :email")
    List<Contrat> findByUtilisateurEmail(@Param("email") String email);
}
