package com.monsociete.dashbordassurance.dto;

import java.time.LocalDate;

public class OperationDTO {

    private Long id;
    private String type;
    private String client;
    private String produit;
    private Double montant;
    private LocalDate date;

    // Constructeurs
    public OperationDTO() {}

    public OperationDTO(Long id, String type, String client, String produit, Double montant, LocalDate date) {
        this.id = id;
        this.type = type;
        this.client = client;
        this.produit = produit;
        this.montant = montant;
        this.date = date;
    }

    // Getters & Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getProduit() {
        return produit;
    }

    public void setProduit(String produit) {
        this.produit = produit;
    }

    public Double getMontant() {
        return montant;
    }

    public void setMontant(Double montant) {
        this.montant = montant;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }
}
