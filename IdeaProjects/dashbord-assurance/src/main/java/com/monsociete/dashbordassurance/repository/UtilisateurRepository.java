package com.monsociete.dashbordassurance.repository;

import com.monsociete.dashbordassurance.model.Utilisateur;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UtilisateurRepository extends JpaRepository<Utilisateur, Long> {

    // Recherche par email (pour l'authentification)
    Optional<Utilisateur> findByEmail(String email);

    // Vérifier si un email existe déjà
    boolean existsByEmail(String email);

    // Recherche par nom et prénom
    List<Utilisateur> findByNomContainingIgnoreCaseOrPrenomContainingIgnoreCase(String nom, String prenom);

    // Recherche par rôle
    List<Utilisateur> findByRole(Utilisateur.Role role);

    // Recherche des utilisateurs actifs
    List<Utilisateur> findByActifTrue();

    // Recherche des utilisateurs inactifs
    List<Utilisateur> findByActifFalse();

    // Recherche par rôle et statut actif
    List<Utilisateur> findByRoleAndActifTrue(Utilisateur.Role role);

    // Recherche avec requête personnalisée
    @Query("SELECT u FROM Utilisateur u WHERE " +
           "(:nom IS NULL OR LOWER(u.nom) LIKE LOWER(CONCAT('%', :nom, '%'))) AND " +
           "(:prenom IS NULL OR LOWER(u.prenom) LIKE LOWER(CONCAT('%', :prenom, '%'))) AND " +
           "(:email IS NULL OR LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%'))) AND " +
           "(:role IS NULL OR u.role = :role) AND " +
           "(:actif IS NULL OR u.actif = :actif)")
    List<Utilisateur> findUtilisateursWithFilters(
            @Param("nom") String nom,
            @Param("prenom") String prenom,
            @Param("email") String email,
            @Param("role") Utilisateur.Role role,
            @Param("actif") Boolean actif
    );

    // Compter les utilisateurs par rôle
    @Query("SELECT u.role, COUNT(u) FROM Utilisateur u WHERE u.actif = true GROUP BY u.role")
    List<Object[]> countUtilisateursByRole();

    // Recherche des clients avec contrats actifs
    @Query("SELECT DISTINCT u FROM Utilisateur u " +
           "JOIN u.contrats c " +
           "WHERE u.role = 'CLIENT' AND c.statut = 'ACTIF'")
    List<Utilisateur> findClientsWithActiveContracts();
}
