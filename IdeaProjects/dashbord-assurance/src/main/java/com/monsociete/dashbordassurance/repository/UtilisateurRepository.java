package com.monsociete.dashbordassurance.repository;

import com.monsociete.dashbordassurance.entity.Utilisateur;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UtilisateurRepository extends JpaRepository<Utilisateur, Long> {
    
    // Recherche par email (pour l'authentification)
    Optional<Utilisateur> findByEmail(String email);
    
    // Vérifier si un email existe déjà
    boolean existsByEmail(String email);
    
    // Recherche par rôle
    List<Utilisateur> findByRole(Utilisateur.Role role);
    
    // Recherche par statut actif
    List<Utilisateur> findByActif(Boolean actif);
    
    // Recherche par nom ou prénom
    List<Utilisateur> findByNomContainingIgnoreCaseOrPrenomContainingIgnoreCase(String nom, String prenom);
    
    // Recherche combinée
    @Query("SELECT u FROM Utilisateur u WHERE " +
           "(:role IS NULL OR u.role = :role) AND " +
           "(:actif IS NULL OR u.actif = :actif) AND " +
           "(:search IS NULL OR LOWER(u.nom) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(u.prenom) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :search, '%')))")
    List<Utilisateur> findWithFilters(@Param("role") Utilisateur.Role role,
                                     @Param("actif") Boolean actif,
                                     @Param("search") String search);
    
    // Statistiques
    @Query("SELECT COUNT(u) FROM Utilisateur u WHERE u.actif = true")
    Long countActiveUsers();
    
    @Query("SELECT u.role, COUNT(u) FROM Utilisateur u GROUP BY u.role")
    List<Object[]> countByRole();
    
    @Query("SELECT COUNT(u) FROM Utilisateur u WHERE u.derniereConnexion >= :since")
    Long countRecentConnections(@Param("since") LocalDateTime since);
    
    @Query("SELECT COUNT(u) FROM Utilisateur u WHERE u.compteVerrouille = true")
    Long countLockedAccounts();
    
    // Utilisateurs récemment créés
    List<Utilisateur> findTop10ByOrderByDateCreationDesc();
    
    // Utilisateurs récemment connectés
    List<Utilisateur> findTop10ByDerniereConnexionIsNotNullOrderByDerniereConnexionDesc();
    
    // Comptes verrouillés
    List<Utilisateur> findByCompteVerrouille(Boolean verrouille);
    
    // Recherche par période de création
    List<Utilisateur> findByDateCreationBetween(LocalDateTime debut, LocalDateTime fin);
}
