package com.monsociete.dashbordassurance;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.CrossOrigin;

@SpringBootApplication
@CrossOrigin(origins = "*")
public class SimpleApplication {

    public static void main(String[] args) {
        System.out.println("🚀 Démarrage de l'application Dashboard Assurance Simple");
        SpringApplication.run(SimpleApplication.class, args);
        System.out.println("✅ Application démarrée avec succès sur http://localhost:8081");
        System.out.println("📋 API disponible sur http://localhost:8081/api/simple");
    }
}
