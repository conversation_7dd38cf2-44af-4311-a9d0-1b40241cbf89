package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class TestController {

    @GetMapping("/test")
    public Map<String, String> test() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "Backend Spring Boot fonctionne !");
        response.put("status", "OK");
        response.put("timestamp", new Date().toString());
        return response;
    }

    @GetMapping("/operations")
    public List<Map<String, Object>> getOperations() {
        List<Map<String, Object>> operations = new ArrayList<>();
        
        // Données de test
        for (int i = 1; i <= 20; i++) {
            Map<String, Object> operation = new HashMap<>();
            operation.put("id", i);
            operation.put("type", i % 3 == 0 ? "RACHAT" : i % 2 == 0 ? "VERSEMENT" : "ARBITRAGE");
            operation.put("clientNom", "Client " + (char)('A' + (i % 10)));
            operation.put("produitNom", i % 4 == 0 ? "Assurance Vie" : i % 3 == 0 ? "PEA" : i % 2 == 0 ? "PERP" : "Multisupport");
            operation.put("montant", 1000 + (i * 500));
            operation.put("date", "2024-0" + (1 + i % 6) + "-" + String.format("%02d", 1 + i % 28));
            operation.put("statut", i % 4 == 0 ? "EN_COURS" : "VALIDEE");
            operation.put("canal", i % 3 == 0 ? "AGENCE" : i % 2 == 0 ? "INTERNET" : "TELEPHONE");
            operations.add(operation);
        }
        
        return operations;
    }
}
