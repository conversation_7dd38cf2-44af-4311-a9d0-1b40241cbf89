package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/api/utilisateurs")
@CrossOrigin(origins = "*")
public class UtilisateurController {

    private static final List<Map<String, Object>> UTILISATEURS_DATA = generateUtilisateursData();

    @GetMapping
    public List<Map<String, Object>> getAllUtilisateurs() {
        return UTILISATEURS_DATA;
    }

    @GetMapping("/{id}")
    public Map<String, Object> getUtilisateurById(@PathVariable Long id) {
        return UTILISATEURS_DATA.stream()
                .filter(user -> user.get("id").equals(id))
                .findFirst()
                .orElse(null);
    }

    @GetMapping("/clients")
    public List<Map<String, Object>> getClients() {
        return UTILISATEURS_DATA.stream()
                .filter(user -> "CLIENT".equals(user.get("role")))
                .toList();
    }

    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> credentials) {
        String email = credentials.get("email");
        String motDePasse = credentials.get("motDePasse");

        Map<String, Object> response = new HashMap<>();

        try {
            System.out.println("🔐 Tentative de connexion pour: " + email);

            // Rechercher l'utilisateur par email
            Map<String, Object> utilisateur = UTILISATEURS_DATA.stream()
                    .filter(user -> email.equals(user.get("email")))
                    .findFirst()
                    .orElse(null);

            if (utilisateur != null && motDePasse.equals(utilisateur.get("motDePasse"))) {
                // Générer un token simple
                String token = "jwt_token_" + utilisateur.get("id") + "_" + System.currentTimeMillis();

                response.put("success", true);
                response.put("message", "Connexion réussie");
                response.put("token", token);
                response.put("utilisateur", Map.of(
                    "id", utilisateur.get("id"),
                    "email", utilisateur.get("email"),
                    "nom", utilisateur.get("nom"),
                    "prenom", utilisateur.get("prenom"),
                    "role", utilisateur.get("role")
                ));

                System.out.println("✅ Connexion réussie pour: " + utilisateur.get("prenom") + " " + utilisateur.get("nom"));

            } else {
                System.out.println("❌ Échec de connexion pour: " + email);
                response.put("success", false);
                response.put("message", "Email ou mot de passe incorrect");
            }

        } catch (Exception e) {
            System.out.println("💥 Erreur de connexion: " + e.getMessage());
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "Erreur lors de la connexion: " + e.getMessage());
        }

        return response;
    }

    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // Statistiques par rôle
        Map<String, Long> statsByRole = UTILISATEURS_DATA.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    u -> u.get("role").toString(),
                    java.util.stream.Collectors.counting()
                ));
        stats.put("statsByRole", statsByRole);

        // Utilisateurs actifs
        long activeUsers = UTILISATEURS_DATA.stream()
                .filter(u -> Boolean.TRUE.equals(u.get("actif")))
                .count();
        stats.put("utilisateursActifs", activeUsers);
        stats.put("totalUtilisateurs", UTILISATEURS_DATA.size());

        return stats;
    }

    private static List<Map<String, Object>> generateUtilisateursData() {
        List<Map<String, Object>> utilisateurs = new ArrayList<>();
        
        // Clients
        String[][] clientsData = {
            {"Jean", "Dupont", "<EMAIL>"},
            {"Marie", "Martin", "<EMAIL>"},
            {"Pierre", "Bernard", "<EMAIL>"},
            {"Sophie", "Dubois", "<EMAIL>"},
            {"Paul", "Thomas", "<EMAIL>"},
            {"Julie", "Robert", "<EMAIL>"},
            {"Michel", "Petit", "<EMAIL>"},
            {"Anne", "Richard", "<EMAIL>"},
            {"François", "Durand", "<EMAIL>"},
            {"Catherine", "Moreau", "<EMAIL>"}
        };
        
        for (int i = 0; i < clientsData.length; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", (long) (i + 1));
            user.put("prenom", clientsData[i][0]);
            user.put("nom", clientsData[i][1]);
            user.put("email", clientsData[i][2]);
            user.put("role", "CLIENT");
            user.put("actif", true);
            user.put("telephone", "0" + (600000000 + i * 1000000 + new Random().nextInt(999999)));
            user.put("adresse", (i + 1) + " Rue de la Paix, 75001 Paris");
            utilisateurs.add(user);
        }
        
        // Personnel
        String[][] personnelData = {
            {"Admin", "Super", "<EMAIL>", "ADMIN"},
            {"Gestionnaire", "Principal", "<EMAIL>", "GESTIONNAIRE"},
            {"Conseiller", "Expert", "<EMAIL>", "CONSEILLER"}
        };
        
        for (int i = 0; i < personnelData.length; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", (long) (clientsData.length + i + 1));
            user.put("prenom", personnelData[i][0]);
            user.put("nom", personnelData[i][1]);
            user.put("email", personnelData[i][2]);
            user.put("role", personnelData[i][3]);
            user.put("actif", true);
            utilisateurs.add(user);
        }
        
        return utilisateurs;
    }
}
