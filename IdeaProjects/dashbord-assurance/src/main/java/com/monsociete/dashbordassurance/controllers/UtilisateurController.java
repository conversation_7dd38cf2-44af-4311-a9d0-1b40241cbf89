package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/api/utilisateurs")
@CrossOrigin(origins = "*")
public class UtilisateurController {

    private static final List<Map<String, Object>> UTILISATEURS_DATA = generateUtilisateursData();

    @GetMapping
    public List<Map<String, Object>> getAllUtilisateurs() {
        return UTILISATEURS_DATA;
    }

    @GetMapping("/{id}/simple")
    public Map<String, Object> getUtilisateurByIdSimple(@PathVariable Long id) {
        return UTILISATEURS_DATA.stream()
                .filter(user -> user.get("id").equals(id))
                .findFirst()
                .orElse(null);
    }

    @GetMapping("/clients")
    public List<Map<String, Object>> getClients() {
        return UTILISATEURS_DATA.stream()
                .filter(user -> "CLIENT".equals(user.get("role")))
                .toList();
    }

    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> credentials) {
        String email = credentials.get("email");
        String motDePasse = credentials.get("motDePasse");

        Map<String, Object> response = new HashMap<>();

        try {
            System.out.println("🔐 Tentative de connexion pour: " + email);

            // Rechercher l'utilisateur par email
            Map<String, Object> utilisateur = UTILISATEURS_DATA.stream()
                    .filter(user -> email.equals(user.get("email")))
                    .findFirst()
                    .orElse(null);

            if (utilisateur != null && motDePasse.equals(utilisateur.get("motDePasse"))) {
                // Générer un token simple
                String token = "jwt_token_" + utilisateur.get("id") + "_" + System.currentTimeMillis();

                response.put("success", true);
                response.put("message", "Connexion réussie");
                response.put("token", token);
                response.put("utilisateur", Map.of(
                    "id", utilisateur.get("id"),
                    "email", utilisateur.get("email"),
                    "nom", utilisateur.get("nom"),
                    "prenom", utilisateur.get("prenom"),
                    "role", utilisateur.get("role"),
                    "telephone", utilisateur.get("telephone"),
                    "adresse", utilisateur.get("adresse")
                ));

                System.out.println("✅ Connexion réussie pour: " + utilisateur.get("prenom") + " " + utilisateur.get("nom"));

            } else {
                System.out.println("❌ Échec de connexion pour: " + email);
                response.put("success", false);
                response.put("message", "Email ou mot de passe incorrect");
            }

        } catch (Exception e) {
            System.out.println("💥 Erreur de connexion: " + e.getMessage());
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "Erreur lors de la connexion: " + e.getMessage());
        }

        return response;
    }

    @PostMapping("/register")
    public Map<String, Object> register(@RequestBody Map<String, String> userData) {
        Map<String, Object> response = new HashMap<>();

        try {
            String email = userData.get("email");
            String nom = userData.get("nom");
            String prenom = userData.get("prenom");
            String motDePasse = userData.get("motDePasse");
            String role = userData.getOrDefault("role", "CLIENT");

            System.out.println("📝 Tentative d'inscription pour: " + email);

            // Vérifier si l'email existe déjà
            boolean emailExists = UTILISATEURS_DATA.stream()
                    .anyMatch(user -> email.equals(user.get("email")));

            if (emailExists) {
                response.put("success", false);
                response.put("message", "Cet email est déjà utilisé");
                return response;
            }

            // Créer le nouvel utilisateur
            Map<String, Object> nouvelUtilisateur = new HashMap<>();
            nouvelUtilisateur.put("id", (long) (UTILISATEURS_DATA.size() + 1));
            nouvelUtilisateur.put("nom", nom);
            nouvelUtilisateur.put("prenom", prenom);
            nouvelUtilisateur.put("email", email);
            nouvelUtilisateur.put("motDePasse", motDePasse);
            nouvelUtilisateur.put("role", role);
            nouvelUtilisateur.put("actif", true);
            nouvelUtilisateur.put("telephone", "01 23 45 67 89");
            nouvelUtilisateur.put("adresse", "Adresse non renseignée");

            UTILISATEURS_DATA.add(nouvelUtilisateur);

            // Générer un token
            String token = "jwt_token_" + nouvelUtilisateur.get("id") + "_" + System.currentTimeMillis();

            response.put("success", true);
            response.put("message", "Inscription réussie");
            response.put("token", token);
            response.put("utilisateur", Map.of(
                "id", nouvelUtilisateur.get("id"),
                "email", nouvelUtilisateur.get("email"),
                "nom", nouvelUtilisateur.get("nom"),
                "prenom", nouvelUtilisateur.get("prenom"),
                "role", nouvelUtilisateur.get("role")
            ));

            System.out.println("✅ Inscription réussie pour: " + prenom + " " + nom);

        } catch (Exception e) {
            System.out.println("💥 Erreur d'inscription: " + e.getMessage());
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "Erreur lors de l'inscription: " + e.getMessage());
        }

        return response;
    }

    @GetMapping("/{id}")
    public Map<String, Object> getUtilisateurById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> utilisateur = UTILISATEURS_DATA.stream()
                    .filter(user -> id.equals(user.get("id")))
                    .findFirst()
                    .orElse(null);

            if (utilisateur != null) {
                response.put("success", true);
                response.put("data", utilisateur);
            } else {
                response.put("success", false);
                response.put("message", "Utilisateur non trouvé");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur: " + e.getMessage());
        }

        return response;
    }

    @PutMapping("/{id}")
    public Map<String, Object> updateUtilisateur(@PathVariable Long id, @RequestBody Map<String, Object> userData) {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> utilisateur = UTILISATEURS_DATA.stream()
                    .filter(user -> id.equals(user.get("id")))
                    .findFirst()
                    .orElse(null);

            if (utilisateur != null) {
                // Mettre à jour les champs
                if (userData.containsKey("nom")) utilisateur.put("nom", userData.get("nom"));
                if (userData.containsKey("prenom")) utilisateur.put("prenom", userData.get("prenom"));
                if (userData.containsKey("email")) utilisateur.put("email", userData.get("email"));
                if (userData.containsKey("telephone")) utilisateur.put("telephone", userData.get("telephone"));
                if (userData.containsKey("adresse")) utilisateur.put("adresse", userData.get("adresse"));
                if (userData.containsKey("role")) utilisateur.put("role", userData.get("role"));
                if (userData.containsKey("actif")) utilisateur.put("actif", userData.get("actif"));

                response.put("success", true);
                response.put("message", "Utilisateur mis à jour avec succès");
                response.put("data", utilisateur);

                System.out.println("✅ Utilisateur mis à jour: " + utilisateur.get("prenom") + " " + utilisateur.get("nom"));

            } else {
                response.put("success", false);
                response.put("message", "Utilisateur non trouvé");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur: " + e.getMessage());
        }

        return response;
    }

    @DeleteMapping("/{id}")
    public Map<String, Object> deleteUtilisateur(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            boolean removed = UTILISATEURS_DATA.removeIf(user -> id.equals(user.get("id")));

            if (removed) {
                response.put("success", true);
                response.put("message", "Utilisateur supprimé avec succès");
                System.out.println("✅ Utilisateur supprimé: ID " + id);
            } else {
                response.put("success", false);
                response.put("message", "Utilisateur non trouvé");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur: " + e.getMessage());
        }

        return response;
    }

    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // Statistiques par rôle
        Map<String, Long> statsByRole = UTILISATEURS_DATA.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    u -> u.get("role").toString(),
                    java.util.stream.Collectors.counting()
                ));
        stats.put("statsByRole", statsByRole);

        // Utilisateurs actifs
        long activeUsers = UTILISATEURS_DATA.stream()
                .filter(u -> Boolean.TRUE.equals(u.get("actif")))
                .count();
        stats.put("utilisateursActifs", activeUsers);
        stats.put("totalUtilisateurs", UTILISATEURS_DATA.size());

        return stats;
    }

    private static List<Map<String, Object>> generateUtilisateursData() {
        List<Map<String, Object>> utilisateurs = new ArrayList<>();
        
        // Clients
        String[][] clientsData = {
            {"Jean", "Dupont", "<EMAIL>"},
            {"Marie", "Martin", "<EMAIL>"},
            {"Pierre", "Bernard", "<EMAIL>"},
            {"Sophie", "Dubois", "<EMAIL>"},
            {"Paul", "Thomas", "<EMAIL>"},
            {"Julie", "Robert", "<EMAIL>"},
            {"Michel", "Petit", "<EMAIL>"},
            {"Anne", "Richard", "<EMAIL>"},
            {"François", "Durand", "<EMAIL>"},
            {"Catherine", "Moreau", "<EMAIL>"}
        };
        
        for (int i = 0; i < clientsData.length; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", (long) (i + 1));
            user.put("prenom", clientsData[i][0]);
            user.put("nom", clientsData[i][1]);
            user.put("email", clientsData[i][2]);
            user.put("role", "CLIENT");
            user.put("actif", true);
            user.put("telephone", "0" + (600000000 + i * 1000000 + new Random().nextInt(999999)));
            user.put("adresse", (i + 1) + " Rue de la Paix, 75001 Paris");
            utilisateurs.add(user);
        }
        
        // Ajouter les mots de passe aux clients
        for (Map<String, Object> user : utilisateurs) {
            user.put("motDePasse", "client123");
        }

        // Personnel avec mots de passe
        String[][] personnelData = {
            {"Admin", "Super", "<EMAIL>", "ADMIN", "admin123"},
            {"Gestionnaire", "Principal", "<EMAIL>", "GESTIONNAIRE", "gestionnaire123"},
            {"Conseiller", "Expert", "<EMAIL>", "CONSEILLER", "conseiller123"},
            {"Client", "Test", "<EMAIL>", "CLIENT", "client123"}
        };

        for (int i = 0; i < personnelData.length; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", (long) (clientsData.length + i + 1));
            user.put("prenom", personnelData[i][0]);
            user.put("nom", personnelData[i][1]);
            user.put("email", personnelData[i][2]);
            user.put("role", personnelData[i][3]);
            user.put("motDePasse", personnelData[i][4]);
            user.put("actif", true);
            user.put("telephone", "01 23 45 67 8" + i);
            user.put("adresse", "Siège social, 123 Avenue des Champs-Élysées, 75008 Paris");
            utilisateurs.add(user);
        }
        
        return utilisateurs;
    }
}
