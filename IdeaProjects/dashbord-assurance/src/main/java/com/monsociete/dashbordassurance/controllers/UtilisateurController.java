package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.entity.Utilisateur;
import com.monsociete.dashbordassurance.service.UtilisateurService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@RestController
@RequestMapping("/api/utilisateurs")
@CrossOrigin(origins = "*")
public class UtilisateurController {

    @Autowired
    private UtilisateurService utilisateurService;

    @GetMapping
    public List<Utilisateur> getAllUtilisateurs() {
        return utilisateurService.getAllUtilisateurs();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Utilisateur> getUtilisateurById(@PathVariable Long id) {
        Optional<Utilisateur> utilisateur = utilisateurService.getUtilisateurById(id);
        return utilisateur.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/clients")
    public List<Utilisateur> getClients() {
        return utilisateurService.getClients();
    }

    @GetMapping("/search")
    public List<Utilisateur> searchUtilisateurs(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean actif,
            @RequestParam(required = false) String search) {

        Utilisateur.Role roleEnum = null;
        if (role != null && !role.isEmpty()) {
            try {
                roleEnum = Utilisateur.Role.valueOf(role.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Ignorer les rôles invalides
            }
        }

        return utilisateurService.getUtilisateursWithFilters(roleEnum, actif, search);
    }

    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        return utilisateurService.getStatistics();
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> createUtilisateur(@RequestBody Utilisateur utilisateur) {
        Map<String, Object> response = new HashMap<>();

        try {
            Utilisateur newUser = utilisateurService.createUtilisateur(utilisateur);

            response.put("success", true);
            response.put("message", "Utilisateur créé avec succès");
            response.put("utilisateur", newUser);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors de la création: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateUtilisateur(@PathVariable Long id, @RequestBody Utilisateur utilisateur) {
        Map<String, Object> response = new HashMap<>();

        try {
            Utilisateur updatedUser = utilisateurService.updateUtilisateur(id, utilisateur);

            response.put("success", true);
            response.put("message", "Utilisateur modifié avec succès");
            response.put("utilisateur", updatedUser);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors de la modification: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, String>> deleteUtilisateur(@PathVariable Long id) {
        Map<String, String> response = new HashMap<>();

        try {
            utilisateurService.deleteUtilisateur(id);

            response.put("success", "true");
            response.put("message", "Utilisateur supprimé avec succès");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", "false");
            response.put("message", "Erreur lors de la suppression: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{id}/unlock")
    public ResponseEntity<Map<String, String>> unlockUtilisateur(@PathVariable Long id) {
        Map<String, String> response = new HashMap<>();

        try {
            utilisateurService.unlockAccount(id);

            response.put("success", "true");
            response.put("message", "Compte déverrouillé avec succès");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", "false");
            response.put("message", "Erreur lors du déverrouillage: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/{id}/profile")
    public ResponseEntity<Map<String, Object>> getUserProfile(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Optional<Utilisateur> utilisateur = utilisateurService.getUtilisateurById(id);

            if (utilisateur.isPresent()) {
                Utilisateur user = utilisateur.get();

                // Créer un profil sans le mot de passe
                Map<String, Object> profile = new HashMap<>();
                profile.put("id", user.getId());
                profile.put("prenom", user.getPrenom());
                profile.put("nom", user.getNom());
                profile.put("email", user.getEmail());
                profile.put("role", user.getRole().toString());
                profile.put("actif", user.getActif());
                profile.put("telephone", user.getTelephone());
                profile.put("adresse", user.getAdresse());
                profile.put("dateCreation", user.getDateCreation());
                profile.put("derniereConnexion", user.getDerniereConnexion());
                profile.put("tentativesConnexion", user.getTentativesConnexion());
                profile.put("compteVerrouille", user.getCompteVerrouille());

                response.put("success", true);
                response.put("profile", profile);

                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "Utilisateur non trouvé");
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors de la récupération du profil: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PutMapping("/{id}/profile")
    public ResponseEntity<Map<String, Object>> updateUserProfile(@PathVariable Long id, @RequestBody Map<String, Object> profileData) {
        Map<String, Object> response = new HashMap<>();

        try {
            Optional<Utilisateur> existingUser = utilisateurService.getUtilisateurById(id);

            if (existingUser.isPresent()) {
                Utilisateur user = existingUser.get();

                // Mettre à jour uniquement les champs autorisés pour le profil
                if (profileData.containsKey("prenom")) {
                    user.setPrenom((String) profileData.get("prenom"));
                }
                if (profileData.containsKey("nom")) {
                    user.setNom((String) profileData.get("nom"));
                }
                if (profileData.containsKey("telephone")) {
                    user.setTelephone((String) profileData.get("telephone"));
                }
                if (profileData.containsKey("adresse")) {
                    user.setAdresse((String) profileData.get("adresse"));
                }

                Utilisateur updatedUser = utilisateurService.saveUtilisateur(user);

                response.put("success", true);
                response.put("message", "Profil mis à jour avec succès");
                response.put("utilisateur", updatedUser);

                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "Utilisateur non trouvé");
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors de la mise à jour du profil: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{id}/change-password")
    public ResponseEntity<Map<String, Object>> changeUserPassword(@PathVariable Long id, @RequestBody Map<String, String> passwordData) {
        Map<String, Object> response = new HashMap<>();

        try {
            String currentPassword = passwordData.get("currentPassword");
            String newPassword = passwordData.get("newPassword");
            String confirmPassword = passwordData.get("confirmPassword");

            // Vérifications
            if (currentPassword == null || newPassword == null || confirmPassword == null) {
                response.put("success", false);
                response.put("message", "Tous les champs sont requis");
                return ResponseEntity.badRequest().body(response);
            }

            if (!newPassword.equals(confirmPassword)) {
                response.put("success", false);
                response.put("message", "Les nouveaux mots de passe ne correspondent pas");
                return ResponseEntity.badRequest().body(response);
            }

            if (newPassword.length() < 6) {
                response.put("success", false);
                response.put("message", "Le mot de passe doit contenir au moins 6 caractères");
                return ResponseEntity.badRequest().body(response);
            }

            // Vérifier l'ancien mot de passe
            Optional<Utilisateur> userOpt = utilisateurService.getUtilisateurById(id);
            if (userOpt.isPresent()) {
                Utilisateur user = userOpt.get();

                // Vérifier l'ancien mot de passe avec le service d'authentification
                if (utilisateurService.authenticate(user.getEmail(), currentPassword)) {
                    // Changer le mot de passe
                    utilisateurService.changePassword(id, newPassword);

                    response.put("success", true);
                    response.put("message", "Mot de passe modifié avec succès");

                    return ResponseEntity.ok(response);
                } else {
                    response.put("success", false);
                    response.put("message", "Mot de passe actuel incorrect");
                    return ResponseEntity.badRequest().body(response);
                }
            } else {
                response.put("success", false);
                response.put("message", "Utilisateur non trouvé");
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors du changement de mot de passe: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/{id}/toggle-status")
    public ResponseEntity<Map<String, Object>> toggleUserStatus(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Optional<Utilisateur> userOpt = utilisateurService.getUtilisateurById(id);

            if (userOpt.isPresent()) {
                Utilisateur user = userOpt.get();
                user.setActif(!user.getActif());

                Utilisateur updatedUser = utilisateurService.saveUtilisateur(user);

                response.put("success", true);
                response.put("message", "Statut utilisateur modifié avec succès");
                response.put("utilisateur", updatedUser);

                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "Utilisateur non trouvé");
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors du changement de statut: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
