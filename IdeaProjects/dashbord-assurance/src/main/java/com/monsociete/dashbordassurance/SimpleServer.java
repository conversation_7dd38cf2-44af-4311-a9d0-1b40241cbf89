package com.monsociete.dashbordassurance;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.*;
import java.util.*;

@SpringBootApplication
public class SimpleServer {

    public static void main(String[] args) {
        System.out.println("🚀 Démarrage du serveur simple Dashboard Assurance");
        SpringApplication.run(SimpleServer.class, args);
        System.out.println("✅ Serveur démarré avec succès sur http://localhost:8080");
        System.out.println("📋 API disponible sur http://localhost:8080/api");
        System.out.println("🔐 Comptes de test :");
        System.out.println("   - <EMAIL> / admin123");
        System.out.println("   - <EMAIL> / gestionnaire123");
        System.out.println("   - <EMAIL> / conseiller123");
        System.out.println("   - <EMAIL> / client123");
    }

    @RestController
    @RequestMapping("/api")
    @CrossOrigin(origins = "*")
    public static class ApiController {

        // Données de test pour les utilisateurs
        private static final List<Map<String, Object>> USERS = new ArrayList<>();
        
        static {
            // Initialiser les utilisateurs de test
            Map<String, Object> admin = new HashMap<>();
            admin.put("id", 1L);
            admin.put("email", "<EMAIL>");
            admin.put("motDePasse", "admin123");
            admin.put("nom", "Admin");
            admin.put("prenom", "Super");
            admin.put("role", "ADMIN");
            admin.put("telephone", "01 23 45 67 80");
            admin.put("adresse", "Siège social, 123 Avenue des Champs-Élysées, 75008 Paris");
            USERS.add(admin);
            
            Map<String, Object> gestionnaire = new HashMap<>();
            gestionnaire.put("id", 2L);
            gestionnaire.put("email", "<EMAIL>");
            gestionnaire.put("motDePasse", "gestionnaire123");
            gestionnaire.put("nom", "Gestionnaire");
            gestionnaire.put("prenom", "Jean");
            gestionnaire.put("role", "GESTIONNAIRE");
            gestionnaire.put("telephone", "01 23 45 67 81");
            gestionnaire.put("adresse", "Agence principale, 456 Rue de Rivoli, 75001 Paris");
            USERS.add(gestionnaire);
            
            Map<String, Object> conseiller = new HashMap<>();
            conseiller.put("id", 3L);
            conseiller.put("email", "<EMAIL>");
            conseiller.put("motDePasse", "conseiller123");
            conseiller.put("nom", "Conseiller");
            conseiller.put("prenom", "Marie");
            conseiller.put("role", "CONSEILLER");
            conseiller.put("telephone", "01 23 45 67 82");
            conseiller.put("adresse", "Agence secondaire, 789 Boulevard Saint-Germain, 75006 Paris");
            USERS.add(conseiller);
            
            Map<String, Object> client = new HashMap<>();
            client.put("id", 4L);
            client.put("email", "<EMAIL>");
            client.put("motDePasse", "client123");
            client.put("nom", "Client");
            client.put("prenom", "Pierre");
            client.put("role", "CLIENT");
            client.put("telephone", "01 23 45 67 83");
            client.put("adresse", "12 Rue de la Paix, 75002 Paris");
            USERS.add(client);
        }

        @PostMapping("/auth/login")
        public Map<String, Object> login(@RequestBody Map<String, String> credentials) {
            String email = credentials.get("email");
            String motDePasse = credentials.get("motDePasse");
            
            Map<String, Object> response = new HashMap<>();
            
            try {
                System.out.println("🔐 Tentative de connexion pour: " + email);
                
                // Rechercher l'utilisateur par email
                Map<String, Object> utilisateur = USERS.stream()
                        .filter(user -> email.equals(user.get("email")))
                        .findFirst()
                        .orElse(null);
                
                if (utilisateur != null && motDePasse.equals(utilisateur.get("motDePasse"))) {
                    // Générer un token simple
                    String token = "simple_token_" + utilisateur.get("id") + "_" + System.currentTimeMillis();
                    
                    response.put("success", true);
                    response.put("message", "Connexion réussie");
                    response.put("token", token);
                    response.put("utilisateur", Map.of(
                        "id", utilisateur.get("id"),
                        "email", utilisateur.get("email"),
                        "nom", utilisateur.get("nom"),
                        "prenom", utilisateur.get("prenom"),
                        "role", utilisateur.get("role"),
                        "telephone", utilisateur.get("telephone"),
                        "adresse", utilisateur.get("adresse")
                    ));
                    
                    System.out.println("✅ Connexion réussie pour: " + utilisateur.get("prenom") + " " + utilisateur.get("nom"));
                    
                } else {
                    System.out.println("❌ Échec de connexion pour: " + email);
                    response.put("success", false);
                    response.put("message", "Email ou mot de passe incorrect");
                }
                
            } catch (Exception e) {
                System.out.println("💥 Erreur de connexion: " + e.getMessage());
                e.printStackTrace();
                response.put("success", false);
                response.put("message", "Erreur lors de la connexion: " + e.getMessage());
            }
            
            return response;
        }

        @PostMapping("/utilisateurs/login")
        public Map<String, Object> loginAlternative(@RequestBody Map<String, String> credentials) {
            // Même logique que /auth/login pour compatibilité
            return login(credentials);
        }

        @GetMapping("/operations/statistics")
        public Map<String, Object> getStatistics() {
            System.out.println("📊 Calcul des statistiques...");
            
            Map<String, Object> stats = new HashMap<>();
            
            // Statistiques de démonstration
            stats.put("totalOperations", 150);
            stats.put("totalMontant", 2500000.0);
            stats.put("montantMoyen", 16666.67);
            stats.put("clientsUniques", 45);
            
            // Statistiques par type
            Map<String, Integer> statsByType = new HashMap<>();
            statsByType.put("SOUSCRIPTION", 68);
            statsByType.put("SINISTRE", 38);
            statsByType.put("AVENANT", 30);
            statsByType.put("RESILIATION", 14);
            stats.put("statsByType", statsByType);
            
            // Statistiques par statut
            Map<String, Integer> statsByStatut = new HashMap<>();
            statsByStatut.put("VALIDEE", 90);
            statsByStatut.put("EN_COURS", 40);
            statsByStatut.put("EN_ATTENTE", 20);
            stats.put("statsByStatut", statsByStatut);
            
            // Top 5 clients
            List<Map<String, Object>> topClients = new ArrayList<>();
            topClients.add(Map.of("nom", "Client Premium A", "montant", 250000.0));
            topClients.add(Map.of("nom", "Client Premium B", "montant", 180000.0));
            topClients.add(Map.of("nom", "Client Premium C", "montant", 150000.0));
            topClients.add(Map.of("nom", "Client Premium D", "montant", 120000.0));
            topClients.add(Map.of("nom", "Client Premium E", "montant", 100000.0));
            stats.put("topClients", topClients);
            
            System.out.println("✅ Statistiques calculées: " + stats);
            return stats;
        }

        @GetMapping("/operations")
        public List<Map<String, Object>> getOperations() {
            System.out.println("📋 Récupération des opérations...");
            
            List<Map<String, Object>> operations = new ArrayList<>();
            
            // Générer des données de démonstration
            String[] types = {"SOUSCRIPTION", "SINISTRE", "AVENANT", "RESILIATION"};
            String[] statuts = {"VALIDEE", "EN_COURS", "EN_ATTENTE"};
            String[] canaux = {"WEB", "AGENCE", "TELEPHONE", "COURRIER"};
            String[] clients = {
                "Jean Dupont", "Marie Martin", "Pierre Bernard", "Sophie Dubois",
                "Paul Thomas", "Julie Robert", "Michel Petit", "Anne Richard",
                "François Durand", "Catherine Moreau", "Alain Leroy", "Sylvie Roux"
            };
            String[] produits = {
                "Auto Premium", "Auto Standard", "Habitation Premium", "Habitation Standard",
                "Vie Premium", "Santé Family", "Voyage Premium", "Responsabilité Civile"
            };
            
            Random random = new Random();
            
            for (int i = 1; i <= 50; i++) {
                Map<String, Object> operation = new HashMap<>();
                operation.put("id", i);
                operation.put("type", types[random.nextInt(types.length)]);
                operation.put("clientNom", clients[random.nextInt(clients.length)]);
                operation.put("produitNom", produits[random.nextInt(produits.length)]);
                operation.put("montant", 100 + random.nextInt(5000));
                operation.put("date", "2024-12-" + String.format("%02d", (i % 28) + 1));
                operation.put("statut", statuts[random.nextInt(statuts.length)]);
                operation.put("canal", canaux[random.nextInt(canaux.length)]);
                operation.put("numeroPolice", "POL" + String.format("%06d", random.nextInt(999999)));
                operation.put("description", "Opération " + operation.get("type") + " pour " + operation.get("produitNom"));
                operations.add(operation);
            }
            
            System.out.println("✅ " + operations.size() + " opérations générées");
            return operations;
        }

        @GetMapping("/utilisateurs")
        public List<Map<String, Object>> getUtilisateurs() {
            System.out.println("👥 Récupération des utilisateurs...");
            return USERS;
        }

        @GetMapping("/test")
        public Map<String, Object> test() {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "OK");
            response.put("message", "🚀 API Simple fonctionne correctement");
            response.put("timestamp", System.currentTimeMillis());
            response.put("endpoints", List.of(
                "/api/auth/login",
                "/api/utilisateurs/login", 
                "/api/operations/statistics",
                "/api/operations",
                "/api/utilisateurs",
                "/api/test"
            ));
            return response;
        }
    }
}
