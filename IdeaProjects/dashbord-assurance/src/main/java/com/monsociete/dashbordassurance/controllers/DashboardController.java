package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.service.OperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private OperationService operationService;

    @GetMapping
    public String dashboard(Model model) {
        // Ajouter les données nécessaires au modèle pour la vue
        model.addAttribute("totalOperations", operationService.getAll().size());
        return "dashboard";
    }

    @GetMapping("/operations")
    public String operations(Model model) {
        model.addAttribute("operations", operationService.getAll());
        return "operations";
    }
}
