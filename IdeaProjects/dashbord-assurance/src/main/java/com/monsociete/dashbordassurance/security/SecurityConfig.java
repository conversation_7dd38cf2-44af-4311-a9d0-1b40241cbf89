package com.monsociete.dashbordassurance.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private JwtRequestFilter jwtRequestFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
                .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint).and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .authorizeHttpRequests(authz -> authz
                        // Endpoints publics
                        .requestMatchers("/api/auth/**").permitAll()
                        .requestMatchers("/h2-console/**").permitAll()
                        .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                        
                        // Endpoints pour les administrateurs
                        .requestMatchers("/api/admin/**").hasRole("ADMIN")
                        
                        // Endpoints pour les gestionnaires et administrateurs
                        .requestMatchers("/api/gestionnaire/**").hasAnyRole("GESTIONNAIRE", "ADMIN")
                        
                        // Endpoints pour les conseillers, gestionnaires et administrateurs
                        .requestMatchers("/api/conseiller/**").hasAnyRole("CONSEILLER", "GESTIONNAIRE", "ADMIN")
                        
                        // API des opérations - accès selon le rôle
                        .requestMatchers("/api/operations/**").hasAnyRole("CLIENT", "CONSEILLER", "GESTIONNAIRE", "ADMIN")
                        .requestMatchers("/api/contrats/**").hasAnyRole("CLIENT", "CONSEILLER", "GESTIONNAIRE", "ADMIN")
                        .requestMatchers("/api/utilisateurs/**").hasAnyRole("CONSEILLER", "GESTIONNAIRE", "ADMIN")
                        
                        // API des statistiques - accès limité
                        .requestMatchers("/api/statistics/**").hasAnyRole("CONSEILLER", "GESTIONNAIRE", "ADMIN")
                        
                        // API d'export - accès limité
                        .requestMatchers("/api/export/**").hasAnyRole("GESTIONNAIRE", "ADMIN")
                        
                        // Dashboard et pages web
                        .requestMatchers("/dashboard/**").hasAnyRole("CONSEILLER", "GESTIONNAIRE", "ADMIN")
                        .requestMatchers("/", "/login", "/css/**", "/js/**", "/images/**").permitAll()
                        
                        // Tout le reste nécessite une authentification
                        .anyRequest().authenticated()
                );

        // Désactiver la protection CSRF pour H2 Console
        http.headers().frameOptions().disable();

        // Ajouter le filtre JWT
        http.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
