package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.dto.AuthRequest;
import com.monsociete.dashbordassurance.dto.AuthResponse;
import com.monsociete.dashbordassurance.dto.RegisterRequest;
import com.monsociete.dashbordassurance.entity.Utilisateur;
import com.monsociete.dashbordassurance.security.JwtUtil;
import com.monsociete.dashbordassurance.security.UserDetailsServiceImpl;
import com.monsociete.dashbordassurance.service.UtilisateurService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private UtilisateurService utilisateurService;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/login")
    public ResponseEntity<?> login(@Valid @RequestBody AuthRequest authRequest) {
        try {
            // Authentifier l'utilisateur
            authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(authRequest.getEmail(), authRequest.getMotDePasse())
            );

            // Charger les détails de l'utilisateur
            UserDetails userDetails = userDetailsService.loadUserByUsername(authRequest.getEmail());
            UserDetailsServiceImpl.UserPrincipal userPrincipal = (UserDetailsServiceImpl.UserPrincipal) userDetails;

            // Générer le token JWT avec les informations utilisateur
            String token = jwtUtil.generateTokenWithUserInfo(
                userDetails.getUsername(),
                userPrincipal.getRole().name(),
                userPrincipal.getId()
            );

            // Créer la réponse
            AuthResponse response = new AuthResponse();
            response.setToken(token);
            response.setType("Bearer");
            response.setUtilisateur(userPrincipal.getUtilisateur());
            response.setExpiresIn(jwtUtil.getExpirationTime(token));

            return ResponseEntity.ok(response);

        } catch (BadCredentialsException e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Identifiants invalides");
            error.put("message", "Email ou mot de passe incorrect");
            return ResponseEntity.badRequest().body(error);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Erreur d'authentification");
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @PostMapping("/register")
    public ResponseEntity<?> register(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            // Vérifier si l'email existe déjà
            if (utilisateurService.existsByEmail(registerRequest.getEmail())) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "Email déjà utilisé");
                error.put("message", "Un compte avec cet email existe déjà");
                return ResponseEntity.badRequest().body(error);
            }

            // Créer le nouvel utilisateur
            Utilisateur nouvelUtilisateur = new Utilisateur();
            nouvelUtilisateur.setNom(registerRequest.getNom());
            nouvelUtilisateur.setPrenom(registerRequest.getPrenom());
            nouvelUtilisateur.setEmail(registerRequest.getEmail());
            nouvelUtilisateur.setMotDePasse(registerRequest.getMotDePasse());
            nouvelUtilisateur.setTelephone(registerRequest.getTelephone());
            nouvelUtilisateur.setAdresse(registerRequest.getAdresse());
            nouvelUtilisateur.setRole(registerRequest.getRole() != null ? registerRequest.getRole() : Utilisateur.Role.CLIENT);

            // Sauvegarder l'utilisateur
            Utilisateur utilisateurCree = utilisateurService.createUtilisateur(nouvelUtilisateur);

            // Générer le token JWT
            UserDetails userDetails = userDetailsService.loadUserByUsername(utilisateurCree.getEmail());
            UserDetailsServiceImpl.UserPrincipal userPrincipal = (UserDetailsServiceImpl.UserPrincipal) userDetails;

            String token = jwtUtil.generateTokenWithUserInfo(
                userDetails.getUsername(),
                userPrincipal.getRole().name(),
                userPrincipal.getId()
            );

            // Créer la réponse
            AuthResponse response = new AuthResponse();
            response.setToken(token);
            response.setType("Bearer");
            response.setUtilisateur(utilisateurCree);
            response.setExpiresIn(jwtUtil.getExpirationTime(token));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Erreur lors de l'inscription");
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @PostMapping("/refresh")
    public ResponseEntity<?> refreshToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "Token manquant");
                error.put("message", "Token d'autorisation requis");
                return ResponseEntity.badRequest().body(error);
            }

            String token = authHeader.substring(7);
            
            if (!jwtUtil.isTokenValid(token)) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "Token invalide");
                error.put("message", "Le token fourni n'est pas valide");
                return ResponseEntity.badRequest().body(error);
            }

            // Extraire les informations du token
            String username = jwtUtil.extractUsername(token);
            String role = jwtUtil.extractRole(token);
            Long userId = jwtUtil.extractUserId(token);

            // Générer un nouveau token
            String newToken = jwtUtil.generateTokenWithUserInfo(username, role, userId);

            // Créer la réponse
            Map<String, Object> response = new HashMap<>();
            response.put("token", newToken);
            response.put("type", "Bearer");
            response.put("expiresIn", jwtUtil.getExpirationTime(newToken));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Erreur lors du rafraîchissement");
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @PostMapping("/logout")
    public ResponseEntity<?> logout() {
        // Dans une implémentation complète, vous pourriez ajouter le token à une blacklist
        Map<String, String> response = new HashMap<>();
        response.put("message", "Déconnexion réussie");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "Token manquant");
                return ResponseEntity.badRequest().body(error);
            }

            String token = authHeader.substring(7);
            String email = jwtUtil.extractUsername(token);
            
            Utilisateur utilisateur = utilisateurService.getUtilisateurByEmail(email)
                    .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé"));

            return ResponseEntity.ok(utilisateur);

        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Erreur lors de la récupération des informations utilisateur");
            error.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
}
