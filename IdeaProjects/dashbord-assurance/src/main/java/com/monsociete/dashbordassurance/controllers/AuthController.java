package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.entity.Utilisateur;
import com.monsociete.dashbordassurance.service.JwtService;
import com.monsociete.dashbordassurance.service.UtilisateurService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {
    
    @Autowired
    private UtilisateurService utilisateurService;
    
    @Autowired
    private JwtService jwtService;
    
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        String email = loginRequest.get("email");
        String motDePasse = loginRequest.get("motDePasse");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Vérifier les identifiants
            if (utilisateurService.authenticate(email, motDePasse)) {
                Optional<Utilisateur> utilisateur = utilisateurService.getUtilisateurByEmail(email);
                
                if (utilisateur.isPresent()) {
                    Utilisateur user = utilisateur.get();
                    
                    // Vérifier si le compte est actif
                    if (!user.getActif()) {
                        response.put("success", false);
                        response.put("message", "Compte désactivé");
                        return ResponseEntity.badRequest().body(response);
                    }
                    
                    // Générer le token JWT
                    String token = jwtService.generateToken(user);
                    
                    response.put("success", true);
                    response.put("message", "Connexion réussie");
                    response.put("token", token);
                    response.put("utilisateur", Map.of(
                        "id", user.getId(),
                        "nom", user.getNomComplet(),
                        "email", user.getEmail(),
                        "role", user.getRole().toString(),
                        "derniereConnexion", user.getDerniereConnexion()
                    ));
                    
                    return ResponseEntity.ok(response);
                }
            }
            
            response.put("success", false);
            response.put("message", "Email ou mot de passe incorrect");
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors de la connexion: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody Utilisateur utilisateur) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Par défaut, les nouveaux utilisateurs sont des clients
            if (utilisateur.getRole() == null) {
                utilisateur.setRole(Utilisateur.Role.CLIENT);
            }
            
            Utilisateur newUser = utilisateurService.createUtilisateur(utilisateur);
            
            response.put("success", true);
            response.put("message", "Utilisateur créé avec succès");
            response.put("utilisateur", Map.of(
                "id", newUser.getId(),
                "nom", newUser.getNomComplet(),
                "email", newUser.getEmail(),
                "role", newUser.getRole().toString()
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors de la création: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @PostMapping("/change-password")
    public ResponseEntity<Map<String, Object>> changePassword(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody Map<String, String> passwordRequest) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String token = authHeader.substring(7); // Enlever "Bearer "
            Long userId = jwtService.extractUserId(token);
            String newPassword = passwordRequest.get("newPassword");
            
            utilisateurService.changePassword(userId, newPassword);
            
            response.put("success", true);
            response.put("message", "Mot de passe modifié avec succès");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors du changement de mot de passe: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @PostMapping("/unlock/{userId}")
    public ResponseEntity<Map<String, Object>> unlockAccount(@PathVariable Long userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            utilisateurService.unlockAccount(userId);
            
            response.put("success", true);
            response.put("message", "Compte déverrouillé avec succès");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors du déverrouillage: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    @GetMapping("/profile")
    public ResponseEntity<Map<String, Object>> getProfile(@RequestHeader("Authorization") String authHeader) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String token = authHeader.substring(7); // Enlever "Bearer "
            Long userId = jwtService.extractUserId(token);
            
            Optional<Utilisateur> utilisateur = utilisateurService.getUtilisateurById(userId);
            
            if (utilisateur.isPresent()) {
                Utilisateur user = utilisateur.get();
                
                response.put("success", true);
                response.put("utilisateur", Map.of(
                    "id", user.getId(),
                    "prenom", user.getPrenom(),
                    "nom", user.getNom(),
                    "email", user.getEmail(),
                    "role", user.getRole().toString(),
                    "telephone", user.getTelephone() != null ? user.getTelephone() : "",
                    "adresse", user.getAdresse() != null ? user.getAdresse() : "",
                    "dateCreation", user.getDateCreation(),
                    "derniereConnexion", user.getDerniereConnexion()
                ));
                
                return ResponseEntity.ok(response);
            }
            
            response.put("success", false);
            response.put("message", "Utilisateur non trouvé");
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Erreur lors de la récupération du profil: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
