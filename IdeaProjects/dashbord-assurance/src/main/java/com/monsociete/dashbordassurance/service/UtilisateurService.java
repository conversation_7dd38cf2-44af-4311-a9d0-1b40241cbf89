package com.monsociete.dashbordassurance.service;

import com.monsociete.dashbordassurance.model.Utilisateur;
import com.monsociete.dashbordassurance.repository.UtilisateurRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UtilisateurService {

    @Autowired
    private UtilisateurRepository utilisateurRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // Récupérer tous les utilisateurs
    public List<Utilisateur> getAllUtilisateurs() {
        return utilisateurRepository.findAll();
    }

    // Récupérer un utilisateur par ID
    public Optional<Utilisateur> getUtilisateurById(Long id) {
        return utilisateurRepository.findById(id);
    }

    // Récupérer un utilisateur par email
    public Optional<Utilisateur> getUtilisateurByEmail(String email) {
        return utilisateurRepository.findByEmail(email);
    }

    // Créer un nouvel utilisateur
    public Utilisateur createUtilisateur(Utilisateur utilisateur) {
        // Vérifier si l'email existe déjà
        if (utilisateurRepository.existsByEmail(utilisateur.getEmail())) {
            throw new RuntimeException("Un utilisateur avec cet email existe déjà");
        }

        // Encoder le mot de passe
        utilisateur.setMotDePasse(passwordEncoder.encode(utilisateur.getMotDePasse()));
        
        return utilisateurRepository.save(utilisateur);
    }

    // Mettre à jour un utilisateur
    public Utilisateur updateUtilisateur(Long id, Utilisateur utilisateurDetails) {
        return utilisateurRepository.findById(id)
                .map(utilisateur -> {
                    utilisateur.setNom(utilisateurDetails.getNom());
                    utilisateur.setPrenom(utilisateurDetails.getPrenom());
                    utilisateur.setTelephone(utilisateurDetails.getTelephone());
                    utilisateur.setAdresse(utilisateurDetails.getAdresse());
                    utilisateur.setRole(utilisateurDetails.getRole());
                    utilisateur.setActif(utilisateurDetails.getActif());
                    
                    // Ne pas mettre à jour l'email et le mot de passe ici
                    return utilisateurRepository.save(utilisateur);
                })
                .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé avec l'ID: " + id));
    }

    // Changer le mot de passe
    public void changerMotDePasse(Long id, String ancienMotDePasse, String nouveauMotDePasse) {
        Utilisateur utilisateur = utilisateurRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé"));

        // Vérifier l'ancien mot de passe
        if (!passwordEncoder.matches(ancienMotDePasse, utilisateur.getMotDePasse())) {
            throw new RuntimeException("Ancien mot de passe incorrect");
        }

        // Encoder et sauvegarder le nouveau mot de passe
        utilisateur.setMotDePasse(passwordEncoder.encode(nouveauMotDePasse));
        utilisateurRepository.save(utilisateur);
    }

    // Activer/Désactiver un utilisateur
    public void toggleUtilisateurStatus(Long id) {
        utilisateurRepository.findById(id)
                .map(utilisateur -> {
                    utilisateur.setActif(!utilisateur.getActif());
                    return utilisateurRepository.save(utilisateur);
                })
                .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé avec l'ID: " + id));
    }

    // Supprimer un utilisateur (soft delete)
    public void deleteUtilisateur(Long id) {
        utilisateurRepository.findById(id)
                .map(utilisateur -> {
                    utilisateur.setActif(false);
                    return utilisateurRepository.save(utilisateur);
                })
                .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé avec l'ID: " + id));
    }

    // Rechercher des utilisateurs avec filtres
    public List<Utilisateur> searchUtilisateurs(String nom, String prenom, String email, 
                                                Utilisateur.Role role, Boolean actif) {
        return utilisateurRepository.findUtilisateursWithFilters(nom, prenom, email, role, actif);
    }

    // Récupérer les utilisateurs par rôle
    public List<Utilisateur> getUtilisateursByRole(Utilisateur.Role role) {
        return utilisateurRepository.findByRoleAndActifTrue(role);
    }

    // Récupérer les clients avec contrats actifs
    public List<Utilisateur> getClientsWithActiveContracts() {
        return utilisateurRepository.findClientsWithActiveContracts();
    }

    // Vérifier si un utilisateur existe
    public boolean existsById(Long id) {
        return utilisateurRepository.existsById(id);
    }

    // Vérifier si un email existe
    public boolean existsByEmail(String email) {
        return utilisateurRepository.existsByEmail(email);
    }

    // Statistiques des utilisateurs par rôle
    public List<Object[]> getStatistiquesByRole() {
        return utilisateurRepository.countUtilisateursByRole();
    }

    // Authentification
    public boolean authenticate(String email, String motDePasse) {
        Optional<Utilisateur> utilisateur = utilisateurRepository.findByEmail(email);
        return utilisateur.isPresent() && 
               utilisateur.get().getActif() && 
               passwordEncoder.matches(motDePasse, utilisateur.get().getMotDePasse());
    }
}
