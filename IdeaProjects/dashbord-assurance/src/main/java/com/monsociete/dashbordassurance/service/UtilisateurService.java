package com.monsociete.dashbordassurance.service;

import com.monsociete.dashbordassurance.entity.Utilisateur;
import com.monsociete.dashbordassurance.repository.UtilisateurRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class UtilisateurService {
    
    @Autowired
    private UtilisateurRepository utilisateurRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    // CRUD Operations
    public List<Utilisateur> getAllUtilisateurs() {
        return utilisateurRepository.findAll();
    }
    
    public Optional<Utilisateur> getUtilisateurById(Long id) {
        return utilisateurRepository.findById(id);
    }
    
    public Optional<Utilisateur> getUtilisateurByEmail(String email) {
        return utilisateurRepository.findByEmail(email);
    }
    
    public List<Utilisateur> getUtilisateursWithFilters(Utilisateur.Role role, Boolean actif, String search) {
        return utilisateurRepository.findWithFilters(role, actif, search);
    }
    
    public List<Utilisateur> getUtilisateursByRole(Utilisateur.Role role) {
        return utilisateurRepository.findByRole(role);
    }
    
    public List<Utilisateur> getClients() {
        return utilisateurRepository.findByRole(Utilisateur.Role.CLIENT);
    }
    
    public Utilisateur saveUtilisateur(Utilisateur utilisateur) {
        // Encoder le mot de passe si c'est un nouvel utilisateur ou si le mot de passe a changé
        if (utilisateur.getId() == null || !utilisateur.getMotDePasse().startsWith("$2a$")) {
            utilisateur.setMotDePasse(passwordEncoder.encode(utilisateur.getMotDePasse()));
        }
        return utilisateurRepository.save(utilisateur);
    }
    
    public Utilisateur createUtilisateur(Utilisateur utilisateur) {
        // Vérifier si l'email existe déjà
        if (utilisateurRepository.existsByEmail(utilisateur.getEmail())) {
            throw new RuntimeException("Un utilisateur avec cet email existe déjà");
        }
        
        // Encoder le mot de passe
        utilisateur.setMotDePasse(passwordEncoder.encode(utilisateur.getMotDePasse()));
        utilisateur.setDateCreation(LocalDateTime.now());
        
        return utilisateurRepository.save(utilisateur);
    }
    
    public Utilisateur updateUtilisateur(Long id, Utilisateur utilisateur) {
        Optional<Utilisateur> existingUser = utilisateurRepository.findById(id);
        if (existingUser.isPresent()) {
            Utilisateur user = existingUser.get();
            user.setPrenom(utilisateur.getPrenom());
            user.setNom(utilisateur.getNom());
            user.setEmail(utilisateur.getEmail());
            user.setRole(utilisateur.getRole());
            user.setActif(utilisateur.getActif());
            user.setTelephone(utilisateur.getTelephone());
            user.setAdresse(utilisateur.getAdresse());
            
            // Ne mettre à jour le mot de passe que s'il est fourni
            if (utilisateur.getMotDePasse() != null && !utilisateur.getMotDePasse().isEmpty()) {
                user.setMotDePasse(passwordEncoder.encode(utilisateur.getMotDePasse()));
            }
            
            return utilisateurRepository.save(user);
        }
        throw new RuntimeException("Utilisateur non trouvé");
    }
    
    public void deleteUtilisateur(Long id) {
        utilisateurRepository.deleteById(id);
    }
    
    // Authentification
    public boolean authenticate(String email, String motDePasse) {
        Optional<Utilisateur> user = utilisateurRepository.findByEmail(email);
        if (user.isPresent()) {
            Utilisateur utilisateur = user.get();
            
            // Vérifier si le compte est verrouillé
            if (utilisateur.getCompteVerrouille()) {
                return false;
            }
            
            // Vérifier le mot de passe
            if (passwordEncoder.matches(motDePasse, utilisateur.getMotDePasse())) {
                // Réinitialiser les tentatives de connexion
                utilisateur.setTentativesConnexion(0);
                utilisateur.setDerniereConnexion(LocalDateTime.now());
                utilisateurRepository.save(utilisateur);
                return true;
            } else {
                // Incrémenter les tentatives de connexion
                utilisateur.setTentativesConnexion(utilisateur.getTentativesConnexion() + 1);
                
                // Verrouiller le compte après 5 tentatives
                if (utilisateur.getTentativesConnexion() >= 5) {
                    utilisateur.setCompteVerrouille(true);
                }
                
                utilisateurRepository.save(utilisateur);
                return false;
            }
        }
        return false;
    }
    
    public void unlockAccount(Long userId) {
        Optional<Utilisateur> user = utilisateurRepository.findById(userId);
        if (user.isPresent()) {
            Utilisateur utilisateur = user.get();
            utilisateur.setCompteVerrouille(false);
            utilisateur.setTentativesConnexion(0);
            utilisateurRepository.save(utilisateur);
        }
    }
    
    public void changePassword(Long userId, String newPassword) {
        Optional<Utilisateur> user = utilisateurRepository.findById(userId);
        if (user.isPresent()) {
            Utilisateur utilisateur = user.get();
            utilisateur.setMotDePasse(passwordEncoder.encode(newPassword));
            utilisateurRepository.save(utilisateur);
        }
    }
    
    // Statistiques
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Statistiques générales
        stats.put("totalUtilisateurs", utilisateurRepository.count());
        stats.put("utilisateursActifs", utilisateurRepository.countActiveUsers());
        stats.put("comptesVerrouilles", utilisateurRepository.countLockedAccounts());
        
        // Connexions récentes (dernières 24h)
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        stats.put("connexionsRecentes", utilisateurRepository.countRecentConnections(yesterday));
        
        // Statistiques par rôle
        Map<String, Long> statsByRole = new HashMap<>();
        List<Object[]> roleStats = utilisateurRepository.countByRole();
        for (Object[] stat : roleStats) {
            Utilisateur.Role role = (Utilisateur.Role) stat[0];
            Long count = (Long) stat[1];
            statsByRole.put(role.toString(), count);
        }
        stats.put("statsByRole", statsByRole);
        
        // Utilisateurs récents
        List<Utilisateur> recentUsers = utilisateurRepository.findTop10ByOrderByDateCreationDesc();
        stats.put("utilisateursRecents", recentUsers);
        
        // Dernières connexions
        List<Utilisateur> recentConnections = utilisateurRepository.findTop10ByDerniereConnexionIsNotNullOrderByDerniereConnexionDesc();
        stats.put("dernieresConnexions", recentConnections);
        
        return stats;
    }
}
