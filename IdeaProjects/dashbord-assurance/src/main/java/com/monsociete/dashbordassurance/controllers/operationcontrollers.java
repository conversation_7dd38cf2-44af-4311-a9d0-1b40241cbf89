package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.service.OperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/operations")
@CrossOrigin(origins = "*") // Autorise les appels depuis le frontend
public class OperationController {

    @Autowired
    private OperationService service;

    // 🔹 GET toutes les opérations
    @GetMapping
    public List<Operation> getAllOperations() {
        return service.getAll();
    }

    // 🔹 GET une seule opération par ID
    @GetMapping("/{id}")
    public ResponseEntity<Operation> getOperationById(@PathVariable Long id) {
        Optional<Operation> op = service.getById(id);
        return op.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    // 🔹 POST créer une nouvelle opération
    @PostMapping
    public ResponseEntity<Operation> createOperation(@RequestBody Operation operation) {
        Operation saved = service.save(operation);
        return ResponseEntity.ok(saved);
    }

    // 🔹 PUT modifier une opération existante
    @PutMapping("/{id}")
    public ResponseEntity<Operation> updateOperation(@PathVariable Long id, @RequestBody Operation updatedOp) {
        Optional<Operation> op = service.getById(id);
        if (op.isPresent()) {
            updatedOp.setId(id);
            return ResponseEntity.ok(service.save(updatedOp));
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    // 🔹 DELETE supprimer une opération
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteOperation(@PathVariable Long id) {
        if (service.exists(id)) {
            service.delete(id);
            return ResponseEntity.noContent().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    // 🔹 GET opérations filtrées (type, client, produit, période)
    @GetMapping("/filter")
    public List<Operation> filterOperations(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String client,
            @RequestParam(required = false) String produit,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate start,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate end
    ) {
        return service.filter(type, client, produit, start, end);
    }
}
