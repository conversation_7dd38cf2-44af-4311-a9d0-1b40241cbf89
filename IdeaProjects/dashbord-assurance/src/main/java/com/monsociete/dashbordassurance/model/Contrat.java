package com.monsociete.dashbordassurance.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "contrats")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Contrat {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "Le numéro de contrat est obligatoire")
    @Column(name = "numero_contrat", nullable = false, unique = true, length = 50)
    private String numeroContrat;

    @NotBlank(message = "Le produit est obligatoire")
    @Column(nullable = false, length = 100)
    private String produit;

    @NotNull(message = "La date d'ouverture est obligatoire")
    @Column(name = "date_ouverture", nullable = false)
    private LocalDate dateOuverture;

    @Column(name = "date_cloture")
    private LocalDate dateCloture;

    @Positive(message = "Le montant initial doit être positif")
    @Column(name = "montant_initial", precision = 15, scale = 2)
    private BigDecimal montantInitial;

    @Column(name = "montant_actuel", precision = 15, scale = 2)
    private BigDecimal montantActuel;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatutContrat statut = StatutContrat.ACTIF;

    @Enumerated(EnumType.STRING)
    @Column(name = "type_produit", nullable = false)
    private TypeProduit typeProduit;

    @Column(name = "taux_rendement", precision = 5, scale = 2)
    private BigDecimal tauxRendement;

    @Column(length = 500)
    private String description;

    @Column(name = "date_creation", nullable = false)
    private LocalDateTime dateCreation;

    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "utilisateur_id", nullable = false)
    private Utilisateur utilisateur;

    @OneToMany(mappedBy = "contrat", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Operation> operations;

    // Enums
    public enum StatutContrat {
        ACTIF,
        SUSPENDU,
        CLOTURE,
        EN_ATTENTE
    }

    public enum TypeProduit {
        ASSURANCE_VIE("Assurance Vie"),
        PEA("Plan d'Épargne en Actions"),
        PERP("Plan d'Épargne Retraite Populaire"),
        MULTISUPPORT("Contrat Multisupport"),
        CONTRAT_CAPITALISATION("Contrat de Capitalisation"),
        PER("Plan d'Épargne Retraite"),
        ASSURANCE_DECES("Assurance Décès");

        private final String libelle;

        TypeProduit(String libelle) {
            this.libelle = libelle;
        }

        public String getLibelle() {
            return libelle;
        }
    }

    // Méthodes utilitaires
    @PrePersist
    protected void onCreate() {
        dateCreation = LocalDateTime.now();
        dateModification = LocalDateTime.now();
        if (montantActuel == null) {
            montantActuel = montantInitial;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        dateModification = LocalDateTime.now();
    }

    public boolean isActif() {
        return statut == StatutContrat.ACTIF;
    }

    public boolean isCloture() {
        return statut == StatutContrat.CLOTURE || dateCloture != null;
    }

    public int getAgeEnAnnees() {
        if (dateCloture != null) {
            return (int) dateOuverture.until(dateCloture).getYears();
        }
        return (int) dateOuverture.until(LocalDate.now()).getYears();
    }

    // Constructeur pour création rapide
    public Contrat(String numeroContrat, String produit, TypeProduit typeProduit, 
                   BigDecimal montantInitial, Utilisateur utilisateur) {
        this.numeroContrat = numeroContrat;
        this.produit = produit;
        this.typeProduit = typeProduit;
        this.montantInitial = montantInitial;
        this.montantActuel = montantInitial;
        this.utilisateur = utilisateur;
        this.dateOuverture = LocalDate.now();
        this.statut = StatutContrat.ACTIF;
    }
}
