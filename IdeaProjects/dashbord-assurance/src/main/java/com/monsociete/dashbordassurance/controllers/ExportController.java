package com.monsociete.dashbordassurance.controllers;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import java.util.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/export")
@CrossOrigin(origins = "*")
public class ExportController {

    @GetMapping("/operations/csv")
    public ResponseEntity<String> exportOperationsCSV(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String client,
            @RequestParam(required = false) String produit) {
        
        // Simuler l'export CSV
        StringBuilder csv = new StringBuilder();
        csv.append("ID,Type,Client,Produit,Montant,Date,Statut,Canal\n");
        
        // Données d'exemple
        for (int i = 1; i <= 10; i++) {
            csv.append(String.format("%d,%s,%s,%s,%.2f,%s,%s,%s\n",
                i, "VERSEMENT", "Client " + i, "Assurance Vie", 
                1000.0 + i * 500, LocalDate.now().minusDays(i), "VALIDEE", "INTERNET"));
        }
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.setContentDispositionFormData("attachment", 
            "operations_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".csv");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(csv.toString());
    }

    @GetMapping("/operations/pdf")
    public ResponseEntity<Map<String, String>> exportOperationsPDF() {
        // Simuler la génération PDF
        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Export PDF généré avec succès");
        response.put("filename", "operations_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf");
        response.put("downloadUrl", "/api/export/download/operations.pdf");
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/contrats/excel")
    public ResponseEntity<Map<String, String>> exportContratsExcel() {
        // Simuler la génération Excel
        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Export Excel généré avec succès");
        response.put("filename", "contrats_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx");
        response.put("downloadUrl", "/api/export/download/contrats.xlsx");
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/rapport/client/{clientId}")
    public ResponseEntity<Map<String, String>> exportRapportClient(@PathVariable Long clientId) {
        // Simuler la génération de rapport client
        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Rapport client généré avec succès");
        response.put("filename", "rapport_client_" + clientId + "_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf");
        response.put("downloadUrl", "/api/export/download/rapport_client_" + clientId + ".pdf");
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/statistics/pdf")
    public ResponseEntity<Map<String, String>> exportStatisticsPDF(
            @RequestParam(required = false) String periode) {
        
        Map<String, String> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Rapport statistiques généré avec succès");
        response.put("filename", "statistiques_" + (periode != null ? periode : "global") + "_" + 
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf");
        response.put("downloadUrl", "/api/export/download/statistiques.pdf");
        
        return ResponseEntity.ok(response);
    }
}
