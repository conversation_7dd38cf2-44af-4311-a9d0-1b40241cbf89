package com.monsociete.dashbordassurance.controllers;

import com.monsociete.dashbordassurance.model.Operation;
import com.monsociete.dashbordassurance.model.Contrat;
import com.monsociete.dashbordassurance.model.Utilisateur;
import com.monsociete.dashbordassurance.service.ExportService;
import com.monsociete.dashbordassurance.service.OperationService;
import com.monsociete.dashbordassurance.service.ContratService;
import com.monsociete.dashbordassurance.service.UtilisateurService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/api/export")
@CrossOrigin(origins = "*")
public class ExportController {

    @Autowired
    private ExportService exportService;

    @Autowired
    private OperationService operationService;

    @Autowired
    private ContratService contratService;

    @Autowired
    private UtilisateurService utilisateurService;

    // Export des opérations en Excel
    @GetMapping("/operations/excel")
    @PreAuthorize("hasAnyRole('GESTIONNAIRE', 'ADMIN')")
    public ResponseEntity<byte[]> exportOperationsExcel(
            @RequestParam(required = false) Operation.TypeOperation type,
            @RequestParam(required = false) Long utilisateurId,
            @RequestParam(required = false) Long contratId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        try {
            // Récupérer les opérations avec filtres
            List<Operation> operations = operationService.searchOperations(
                type, utilisateurId, contratId, null, null, null, 
                dateDebut, dateFin, null, null
            );

            // Générer le fichier Excel
            byte[] excelData = exportService.exportOperationsToExcel(operations);

            // Préparer la réponse
            String filename = "operations_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(excelData.length);

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Export des contrats en Excel
    @GetMapping("/contrats/excel")
    @PreAuthorize("hasAnyRole('GESTIONNAIRE', 'ADMIN')")
    public ResponseEntity<byte[]> exportContratsExcel(
            @RequestParam(required = false) Long utilisateurId,
            @RequestParam(required = false) Contrat.StatutContrat statut,
            @RequestParam(required = false) Contrat.TypeProduit typeProduit) {
        
        try {
            // Récupérer les contrats avec filtres
            List<Contrat> contrats = contratService.searchContrats(
                utilisateurId, statut, typeProduit, null, null, null, null, null
            );

            // Générer le fichier Excel
            byte[] excelData = exportService.exportContratsToExcel(contrats);

            // Préparer la réponse
            String filename = "contrats_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(excelData.length);

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Export des opérations en PDF
    @GetMapping("/operations/pdf")
    @PreAuthorize("hasAnyRole('GESTIONNAIRE', 'ADMIN')")
    public ResponseEntity<byte[]> exportOperationsPdf(
            @RequestParam(required = false) Operation.TypeOperation type,
            @RequestParam(required = false) Long utilisateurId,
            @RequestParam(required = false) Long contratId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin,
            @RequestParam(required = false) String titre) {
        
        try {
            // Récupérer les opérations avec filtres
            List<Operation> operations = operationService.searchOperations(
                type, utilisateurId, contratId, null, null, null, 
                dateDebut, dateFin, null, null
            );

            // Générer le fichier PDF
            byte[] pdfData = exportService.exportOperationsToPdf(operations, titre);

            // Préparer la réponse
            String filename = "operations_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(pdfData.length);

            return new ResponseEntity<>(pdfData, headers, HttpStatus.OK);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Export d'un rapport client complet en PDF
    @GetMapping("/client/{clientId}/rapport")
    @PreAuthorize("hasAnyRole('CONSEILLER', 'GESTIONNAIRE', 'ADMIN')")
    public ResponseEntity<byte[]> exportRapportClient(
            @PathVariable Long clientId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        try {
            // Récupérer le client
            Utilisateur client = utilisateurService.getUtilisateurById(clientId)
                    .orElseThrow(() -> new RuntimeException("Client non trouvé"));

            // Récupérer les contrats du client
            List<Contrat> contrats = contratService.getContratsByUtilisateur(clientId);

            // Récupérer les opérations du client avec filtres de date
            List<Operation> operations = operationService.getOperationsByUtilisateur(clientId);
            
            // Filtrer par date si spécifié
            if (dateDebut != null || dateFin != null) {
                operations = operations.stream()
                    .filter(op -> {
                        if (dateDebut != null && op.getDate().isBefore(dateDebut)) return false;
                        if (dateFin != null && op.getDate().isAfter(dateFin)) return false;
                        return true;
                    })
                    .toList();
            }

            // Générer le rapport PDF
            byte[] pdfData = exportService.exportRapportClientToPdf(client, contrats, operations);

            // Préparer la réponse
            String filename = "rapport_client_" + client.getNom() + "_" + 
                            LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(pdfData.length);

            return new ResponseEntity<>(pdfData, headers, HttpStatus.OK);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Export des statistiques en PDF
    @GetMapping("/statistiques/pdf")
    @PreAuthorize("hasAnyRole('GESTIONNAIRE', 'ADMIN')")
    public ResponseEntity<byte[]> exportStatistiquesPdf(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateDebut,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate dateFin) {
        
        try {
            // Récupérer toutes les opérations pour les statistiques
            List<Operation> operations = operationService.searchOperations(
                null, null, null, null, null, null, 
                dateDebut, dateFin, null, null
            );

            String titre = "Rapport Statistiques";
            if (dateDebut != null && dateFin != null) {
                titre += " du " + dateDebut.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")) +
                        " au " + dateFin.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            }

            // Générer le fichier PDF
            byte[] pdfData = exportService.exportOperationsToPdf(operations, titre);

            // Préparer la réponse
            String filename = "statistiques_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".pdf";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setContentLength(pdfData.length);

            return new ResponseEntity<>(pdfData, headers, HttpStatus.OK);

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
