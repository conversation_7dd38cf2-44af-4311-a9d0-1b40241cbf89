<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Assurance Professionnel</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <style>
        :root {
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .welcome-container {
            text-align: center;
            max-width: 600px;
            padding: 3rem;
        }

        .welcome-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            margin: 0 auto 2rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .welcome-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .welcome-subtitle {
            font-size: 1.25rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .btn-welcome {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 1rem;
            font-weight: 600;
            font-size: 1.125rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
        }

        .btn-welcome:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .features {
            margin-top: 4rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 2rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .feature:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-4px);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .feature-description {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .fade-in {
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .welcome-container {
                padding: 2rem 1rem;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .welcome-subtitle {
                font-size: 1rem;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container fade-in">
        <div class="welcome-logo">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1 class="welcome-title">AssuranceBoard Pro</h1>
        <p class="welcome-subtitle">
            Tableau de bord professionnel pour la gestion d'assurance
        </p>
        
        <a href="login-professionnel.html" class="btn-welcome">
            <i class="fas fa-sign-in-alt"></i>
            Accéder au Dashboard
        </a>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-title">Analytics Avancés</div>
                <div class="feature-description">
                    Visualisez vos données avec des graphiques interactifs et des KPI en temps réel
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="feature-title">Gestion Utilisateurs</div>
                <div class="feature-description">
                    Gérez les utilisateurs avec des rôles et permissions granulaires
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="feature-title">Suivi Opérations</div>
                <div class="feature-description">
                    Suivez toutes vos opérations d'assurance en temps réel
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-file-export"></i>
                </div>
                <div class="feature-title">Export Données</div>
                <div class="feature-description">
                    Exportez vos rapports en Excel, PDF et autres formats
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="feature-title">Sécurité Renforcée</div>
                <div class="feature-description">
                    Authentification JWT et protection des données sensibles
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="feature-title">Responsive Design</div>
                <div class="feature-description">
                    Interface adaptée à tous les appareils et tailles d'écran
                </div>
            </div>
        </div>
    </div>

    <script>
        // Redirection automatique si déjà connecté
        document.addEventListener('DOMContentLoaded', function() {
            const authToken = localStorage.getItem('authToken');
            if (authToken) {
                console.log('Utilisateur déjà connecté, redirection...');
                window.location.href = 'dashboard-professionnel.html';
            }
        });
    </script>
</body>
</html>
