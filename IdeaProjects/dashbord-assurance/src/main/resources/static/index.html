<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Assurance - Connexion</title>

    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <style>
        :root {
            --primary: #1e40af;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON>go<PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0f172a;
        }

        .welcome-container {
            text-align: center;
            max-width: 800px;
            padding: 3rem;
        }

        .welcome-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            margin: 0 auto 2rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .welcome-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .welcome-subtitle {
            font-size: 1.25rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .btn-welcome {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 1rem;
            font-weight: 600;
            font-size: 1.125rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            margin: 0.5rem;
        }

        .btn-welcome:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .features {
            margin-top: 4rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 2rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .feature:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-4px);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .feature-description {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .fade-in {
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-badge {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            border-radius: 1rem;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            display: inline-block;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        @media (max-width: 768px) {
            .welcome-container {
                padding: 2rem 1rem;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .welcome-subtitle {
                font-size: 1rem;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .status-badge {
                top: 1rem;
                right: 1rem;
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Status badge -->
    <div class="status-badge">
        <div class="d-flex align-items-center">
            <span class="status-dot"></span>
            <small>Projet Spring Boot Actif</small>
        </div>
    </div>

    <div class="welcome-container fade-in">
        <div class="welcome-logo">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1 class="welcome-title">Dashboard Assurance</h1>
        <p class="welcome-subtitle">
            Projet Spring Boot - 100% Opérationnel<br>
            Tableau de bord professionnel intégré avec API Node.js
        </p>
        
        <div class="d-flex flex-wrap justify-content-center">
            <a href="login.html" class="btn-welcome">
                <i class="fas fa-sign-in-alt"></i>
                Se connecter
            </a>
            <a href="dashboard.html" class="btn-welcome">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard Principal
            </a>
            <a href="dashboard-boutons-fonctionnels.html" class="btn-welcome">
                <i class="fas fa-mouse-pointer"></i>
                Test des Boutons
            </a>
        </div>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-title">Analytics Avancés</div>
                <div class="feature-description">
                    Visualisez vos données avec des graphiques interactifs Chart.js et des KPI en temps réel depuis l'API Node.js
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="feature-title">Gestion Utilisateurs</div>
                <div class="feature-description">
                    4 rôles intégrés (Admin, Gestionnaire, Conseiller, Client) avec authentification JWT sécurisée
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="feature-title">Suivi Opérations</div>
                <div class="feature-description">
                    50 opérations de démonstration avec 128,748€ de volume, filtres et recherche avancée
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-file-export"></i>
                </div>
                <div class="feature-title">Export Données</div>
                <div class="feature-description">
                    Exportez vos rapports en Excel et PDF avec DataTables intégré et boutons fonctionnels
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="feature-title">Sécurité Renforcée</div>
                <div class="feature-description">
                    Authentification JWT, protection CORS, et validation des données côté serveur
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="feature-title">Responsive Design</div>
                <div class="feature-description">
                    Interface Bootstrap 5 adaptée à tous les appareils avec animations CSS3 fluides
                </div>
            </div>
        </div>

        <div class="mt-5">
            <div class="alert alert-light bg-transparent border-light text-white">
                <h5><i class="fas fa-info-circle me-2"></i>Projet Intégré</h5>
                <p class="mb-0">
                    Ce dashboard est maintenant <strong>100% intégré</strong> dans votre projet Spring Boot avec :
                    <br>• API Node.js opérationnelle (port 8081)
                    <br>• Interface professionnelle responsive
                    <br>• Données réelles et graphiques interactifs
                    <br>• Authentification JWT fonctionnelle
                </p>
            </div>
        </div>
    </div>

    <script>
        // Redirection automatique si déjà connecté
        document.addEventListener('DOMContentLoaded', function() {
            const authToken = localStorage.getItem('authToken');
            if (authToken) {
                console.log('Utilisateur déjà connecté, redirection...');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1000);
            }
        });

        console.log('🏠 Page d\'accueil Dashboard Assurance - Projet Spring Boot Professionnel');
    </script>
</body>
</html>
