// Variables globales pour les graphiques
let typeChart, client<PERSON>hart, timelineChart;
let currentFilters = {
    type: '',
    client: '',
    product: '',
    startDate: '',
    endDate: ''
};

// Initialisation au chargement de la page
$(document).ready(function() {
    initializeDateRangePicker();
    loadInitialData();
    setupEventListeners();
});

// Configuration du sélecteur de dates
function initializeDateRangePicker() {
    $('#dateRange').daterangepicker({
        startDate: moment().subtract(30, 'days'),
        endDate: moment(),
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'Appliquer',
            cancelLabel: 'Annuler',
            fromLabel: 'De',
            toLabel: 'À',
            customRangeLabel: 'Personnalisé',
            weekLabel: 'S',
            daysOfWeek: ['Di', 'Lu', 'Ma', 'Me', 'Je', 'Ve', 'Sa'],
            monthNames: ['Janvier', '<PERSON>évrier', '<PERSON>', 'A<PERSON><PERSON>', '<PERSON>', 'Juin',
                        '<PERSON><PERSON><PERSON>', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
            firstDay: 1
        },
        ranges: {
            'Aujourd\'hui': [moment(), moment()],
            'Hier': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            '7 derniers jours': [moment().subtract(6, 'days'), moment()],
            '30 derniers jours': [moment().subtract(29, 'days'), moment()],
            'Ce mois': [moment().startOf('month'), moment().endOf('month')],
            'Mois dernier': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    });
}

// Configuration des écouteurs d'événements
function setupEventListeners() {
    // Changement de filtres
    $('#typeFilter, #clientFilter, #productFilter').on('change', function() {
        // Auto-application des filtres après un délai
        setTimeout(applyFilters, 500);
    });

    // Changement de période
    $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
        currentFilters.startDate = picker.startDate.format('YYYY-MM-DD');
        currentFilters.endDate = picker.endDate.format('YYYY-MM-DD');
        applyFilters();
    });
}

// Chargement initial des données
function loadInitialData() {
    showLoading();
    
    // Charger les options des filtres
    loadFilterOptions();
    
    // Charger les KPIs
    loadKPIs();
    
    // Charger les graphiques
    loadCharts();
    
    hideLoading();
}

// Chargement des options pour les filtres
function loadFilterOptions() {
    // Charger les clients
    $.get('/api/operations', function(operations) {
        const clients = [...new Set(operations.map(op => op.client))];
        const clientSelect = $('#clientFilter');
        clientSelect.empty().append('<option value="">Tous les clients</option>');
        clients.forEach(client => {
            clientSelect.append(`<option value="${client}">${client}</option>`);
        });

        // Charger les produits
        const products = [...new Set(operations.map(op => op.produit))];
        const productSelect = $('#productFilter');
        productSelect.empty().append('<option value="">Tous les produits</option>');
        products.forEach(product => {
            productSelect.append(`<option value="${product}">${product}</option>`);
        });
    });
}

// Chargement des KPIs
function loadKPIs() {
    const params = buildFilterParams();
    
    $.get('/api/statistics/kpis' + params, function(data) {
        $('#totalOperations').text(data.totalOperations.toLocaleString());
        $('#totalAmount').text(formatCurrency(data.totalAmount));
        $('#averageAmount').text(formatCurrency(data.averageAmount));
        $('#uniqueClients').text(data.uniqueClients);
    }).fail(function() {
        console.error('Erreur lors du chargement des KPIs');
    });
}

// Chargement des graphiques
function loadCharts() {
    loadTypeChart();
    loadClientChart();
    loadTimelineChart();
}

// Graphique par type d'opération
function loadTypeChart() {
    const params = buildFilterParams();
    
    $.get('/api/statistics/by-type' + params, function(data) {
        const ctx = document.getElementById('typeChart').getContext('2d');
        
        if (typeChart) {
            typeChart.destroy();
        }
        
        typeChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(data.amount),
                datasets: [{
                    data: Object.values(data.amount),
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + formatCurrency(context.parsed);
                            }
                        }
                    }
                }
            }
        });
    });
}

// Graphique par client
function loadClientChart() {
    const params = buildFilterParams();
    
    $.get('/api/statistics/by-client' + params, function(data) {
        const ctx = document.getElementById('clientChart').getContext('2d');
        
        if (clientChart) {
            clientChart.destroy();
        }
        
        // Trier et prendre les 5 premiers clients
        const sortedClients = Object.entries(data.amount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);
        
        clientChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sortedClients.map(([client]) => client),
                datasets: [{
                    label: 'Montant (€)',
                    data: sortedClients.map(([, amount]) => amount),
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Montant: ' + formatCurrency(context.parsed.y);
                            }
                        }
                    }
                }
            }
        });
    });
}

// Graphique d'évolution temporelle
function loadTimelineChart() {
    const params = buildFilterParams();
    
    $.get('/api/statistics/timeline' + params, function(data) {
        const ctx = document.getElementById('timelineChart').getContext('2d');
        
        if (timelineChart) {
            timelineChart.destroy();
        }
        
        // Trier les dates
        const sortedDates = Object.entries(data.amount)
            .sort(([a], [b]) => new Date(a) - new Date(b));
        
        timelineChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: sortedDates.map(([date]) => moment(date).format('DD/MM')),
                datasets: [{
                    label: 'Montant (€)',
                    data: sortedDates.map(([, amount]) => amount),
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Montant: ' + formatCurrency(context.parsed.y);
                            }
                        }
                    }
                }
            }
        });
    });
}

// Application des filtres
function applyFilters() {
    currentFilters.type = $('#typeFilter').val();
    currentFilters.client = $('#clientFilter').val();
    currentFilters.product = $('#productFilter').val();
    
    showLoading();
    loadKPIs();
    loadCharts();
    hideLoading();
}

// Réinitialisation des filtres
function resetFilters() {
    $('#typeFilter').val('');
    $('#clientFilter').val('');
    $('#productFilter').val('');
    $('#dateRange').data('daterangepicker').setStartDate(moment().subtract(30, 'days'));
    $('#dateRange').data('daterangepicker').setEndDate(moment());
    
    currentFilters = {
        type: '',
        client: '',
        product: '',
        startDate: '',
        endDate: ''
    };
    
    applyFilters();
}

// Actualisation des données
function refreshData() {
    loadInitialData();
}

// Construction des paramètres de filtre pour les requêtes
function buildFilterParams() {
    const params = new URLSearchParams();
    
    if (currentFilters.type) params.append('type', currentFilters.type);
    if (currentFilters.client) params.append('client', currentFilters.client);
    if (currentFilters.product) params.append('produit', currentFilters.product);
    if (currentFilters.startDate) params.append('start', currentFilters.startDate);
    if (currentFilters.endDate) params.append('end', currentFilters.endDate);
    
    return params.toString() ? '?' + params.toString() : '';
}

// Formatage des montants
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

// Fonctions utilitaires
function showLoading() {
    $('.loading').show();
}

function hideLoading() {
    $('.loading').hide();
}
