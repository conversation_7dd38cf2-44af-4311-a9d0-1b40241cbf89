// Variable globale pour la table DataTables
let operationsTable;

// Initialisation au chargement de la page
$(document).ready(function() {
    initializeTable();
    loadFilterOptions();
    setupEventListeners();
});

// Initialisation de la table DataTables
function initializeTable() {
    operationsTable = $('#operationsTable').DataTable({
        ajax: {
            url: '/api/operations',
            dataSrc: ''
        },
        columns: [
            { data: 'id' },
            { 
                data: 'type',
                render: function(data) {
                    return `<span class="badge ${data}">${data}</span>`;
                }
            },
            { data: 'client' },
            { data: 'produit' },
            { 
                data: 'montant',
                render: function(data) {
                    return formatCurrency(data);
                }
            },
            { 
                data: 'date',
                render: function(data) {
                    return moment(data).format('DD/MM/YYYY');
                }
            },
            {
                data: null,
                orderable: false,
                render: function(data, type, row) {
                    return `
                        <button class="btn btn-sm btn-outline-primary" onclick="viewOperation(${row.id})" title="Voir détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editOperation(${row.id})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteOperation(${row.id})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    `;
                }
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json'
        },
        responsive: true,
        pageLength: 25,
        order: [[5, 'desc']], // Trier par date décroissante
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm'
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> Imprimer',
                className: 'btn btn-info btn-sm'
            }
        ]
    });
}

// Chargement des options pour les filtres
function loadFilterOptions() {
    $.get('/api/operations', function(operations) {
        // Charger les clients
        const clients = [...new Set(operations.map(op => op.client))];
        const clientSelect = $('#clientFilter');
        clientSelect.empty().append('<option value="">Tous les clients</option>');
        clients.forEach(client => {
            clientSelect.append(`<option value="${client}">${client}</option>`);
        });

        // Charger les produits
        const products = [...new Set(operations.map(op => op.produit))];
        const productSelect = $('#productFilter');
        productSelect.empty().append('<option value="">Tous les produits</option>');
        products.forEach(product => {
            productSelect.append(`<option value="${product}">${product}</option>`);
        });
    });
}

// Configuration des écouteurs d'événements
function setupEventListeners() {
    // Filtres en temps réel
    $('#typeFilter, #clientFilter, #productFilter').on('change', function() {
        applyFilters();
    });

    $('#amountFilter').on('input', function() {
        // Délai pour éviter trop de requêtes
        clearTimeout(this.delay);
        this.delay = setTimeout(applyFilters, 500);
    });
}

// Application des filtres
function applyFilters() {
    const type = $('#typeFilter').val();
    const client = $('#clientFilter').val();
    const product = $('#productFilter').val();
    const minAmount = $('#amountFilter').val();

    // Filtrer côté client avec DataTables
    operationsTable.columns(1).search(type);
    operationsTable.columns(2).search(client);
    operationsTable.columns(3).search(product);
    
    // Filtre personnalisé pour le montant
    $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
        if (minAmount === '') return true;
        
        const amount = parseFloat(data[4].replace(/[€\s,]/g, '').replace(',', '.'));
        return amount >= parseFloat(minAmount);
    });

    operationsTable.draw();
}

// Réinitialisation des filtres
function resetFilters() {
    $('#typeFilter').val('');
    $('#clientFilter').val('');
    $('#productFilter').val('');
    $('#amountFilter').val('');
    
    // Réinitialiser les filtres DataTables
    operationsTable.search('').columns().search('').draw();
    
    // Supprimer les filtres personnalisés
    $.fn.dataTable.ext.search.pop();
}

// Actualisation de la table
function refreshTable() {
    operationsTable.ajax.reload();
    loadFilterOptions();
}

// Voir les détails d'une opération
function viewOperation(id) {
    $.get(`/api/operations/${id}`, function(operation) {
        const details = `
            <div class="row">
                <div class="col-md-6">
                    <strong>ID:</strong> ${operation.id}<br>
                    <strong>Type:</strong> <span class="badge ${operation.type}">${operation.type}</span><br>
                    <strong>Client:</strong> ${operation.client}<br>
                </div>
                <div class="col-md-6">
                    <strong>Produit:</strong> ${operation.produit}<br>
                    <strong>Montant:</strong> ${formatCurrency(operation.montant)}<br>
                    <strong>Date:</strong> ${moment(operation.date).format('DD/MM/YYYY')}<br>
                </div>
            </div>
        `;
        
        $('#operationDetails').html(details);
        $('#operationModal').modal('show');
    }).fail(function() {
        alert('Erreur lors du chargement des détails de l\'opération');
    });
}

// Modifier une opération
function editOperation(id) {
    // Ici vous pouvez implémenter la logique de modification
    // Par exemple, ouvrir un modal avec un formulaire
    alert('Fonctionnalité de modification à implémenter');
}

// Supprimer une opération
function deleteOperation(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette opération ?')) {
        $.ajax({
            url: `/api/operations/${id}`,
            type: 'DELETE',
            success: function() {
                operationsTable.ajax.reload();
                alert('Opération supprimée avec succès');
            },
            error: function() {
                alert('Erreur lors de la suppression de l\'opération');
            }
        });
    }
}

// Exporter les données
function exportData() {
    // Utiliser les boutons DataTables pour l'export
    $('.buttons-excel').click();
}

// Formatage des montants
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Fonction pour moment.js (si pas déjà inclus)
if (typeof moment === 'undefined') {
    window.moment = function(date) {
        return {
            format: function(format) {
                const d = new Date(date);
                if (format === 'DD/MM/YYYY') {
                    return d.toLocaleDateString('fr-FR');
                }
                return d.toLocaleDateString();
            }
        };
    };
}
