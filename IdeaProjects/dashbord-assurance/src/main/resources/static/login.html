<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Assurance - Connexion</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #1e40af;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystem<PERSON>ont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0f172a;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1.5rem;
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 2rem;
        }

        .login-title {
            text-align: center;
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #0f172a;
        }

        .login-subtitle {
            text-align: center;
            color: #64748b;
            margin-bottom: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
        }

        .btn-login {
            background: var(--gradient-primary);
            border: none;
            border-radius: 0.75rem;
            padding: 1rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            color: white;
        }

        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
        }

        .demo-accounts {
            border-top: 1px solid #e2e8f0;
            padding-top: 1.5rem;
            margin-top: 1.5rem;
        }

        .demo-account {
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid transparent;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .demo-account:hover {
            border-color: var(--primary);
            background: rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }

        .demo-account-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .demo-account-icon {
            width: 40px;
            height: 40px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .demo-account-icon.admin {
            background: var(--danger);
        }

        .demo-account-icon.gestionnaire {
            background: var(--warning);
        }

        .demo-account-icon.conseiller {
            background: var(--primary);
        }

        .demo-account-icon.client {
            background: var(--success);
        }

        .alert {
            border: none;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .footer-links {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e2e8f0;
        }

        .footer-links a {
            color: #64748b;
            text-decoration: none;
            margin: 0 1rem;
            font-size: 0.875rem;
        }

        .footer-links a:hover {
            color: var(--primary);
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container fade-in">
        <!-- Logo et titre -->
        <div class="login-logo">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1 class="login-title">Dashboard Assurance</h1>
        <p class="login-subtitle">Connectez-vous à votre espace professionnel</p>
        
        <!-- Alertes -->
        <div id="alertContainer"></div>
        
        <!-- Formulaire de connexion -->
        <form id="loginForm">
            <div class="form-floating">
                <input type="email" class="form-control" id="email" placeholder="Email" required>
                <label for="email">
                    <i class="fas fa-envelope me-2"></i>
                    Adresse email
                </label>
            </div>
            
            <div class="form-floating">
                <input type="password" class="form-control" id="password" placeholder="Mot de passe" required>
                <label for="password">
                    <i class="fas fa-lock me-2"></i>
                    Mot de passe
                </label>
            </div>
            
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    Se souvenir de moi
                </label>
            </div>
            
            <button type="submit" class="btn btn-login" id="loginBtn">
                <span id="loginBtnText">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Se connecter
                </span>
                <span id="loginBtnSpinner" class="d-none">
                    <span class="spinner-border spinner-border-sm me-2"></span>
                    Connexion en cours...
                </span>
            </button>
        </form>
        
        <!-- Comptes de démonstration -->
        <div class="demo-accounts">
            <h6 class="text-center mb-3 fw-bold text-muted">
                <i class="fas fa-users me-2"></i>
                Comptes de démonstration
            </h6>
            
            <div class="demo-account" onclick="quickLogin('<EMAIL>', 'admin123', 'ADMIN')">
                <div class="demo-account-info">
                    <div class="demo-account-icon admin">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div>
                        <div class="fw-bold">Administrateur</div>
                        <small class="text-muted">Accès complet au système</small>
                    </div>
                </div>
                <span class="badge bg-danger">ADMIN</span>
            </div>
            
            <div class="demo-account" onclick="quickLogin('<EMAIL>', 'gestionnaire123', 'GESTIONNAIRE')">
                <div class="demo-account-info">
                    <div class="demo-account-icon gestionnaire">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <div class="fw-bold">Gestionnaire</div>
                        <small class="text-muted">Gestion des opérations</small>
                    </div>
                </div>
                <span class="badge bg-warning">GESTIONNAIRE</span>
            </div>
            
            <div class="demo-account" onclick="quickLogin('<EMAIL>', 'conseiller123', 'CONSEILLER')">
                <div class="demo-account-info">
                    <div class="demo-account-icon conseiller">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div>
                        <div class="fw-bold">Conseiller</div>
                        <small class="text-muted">Conseil clientèle</small>
                    </div>
                </div>
                <span class="badge bg-primary">CONSEILLER</span>
            </div>
            
            <div class="demo-account" onclick="quickLogin('<EMAIL>', 'client123', 'CLIENT')">
                <div class="demo-account-info">
                    <div class="demo-account-icon client">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="fw-bold">Client</div>
                        <small class="text-muted">Espace personnel</small>
                    </div>
                </div>
                <span class="badge bg-success">CLIENT</span>
            </div>
        </div>
        
        <!-- Liens du footer -->
        <div class="footer-links">
            <a href="#" onclick="showHelp()">Aide</a>
            <a href="#" onclick="showContact()">Contact</a>
            <a href="#" onclick="showPrivacy()">Confidentialité</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <script>
        // Configuration
        const API_URL = 'http://localhost:8081/api';
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔐 Initialisation page de connexion');
            
            // Vérifier si l'utilisateur est déjà connecté
            const savedToken = localStorage.getItem('authToken');
            if (savedToken) {
                console.log('Utilisateur déjà connecté, redirection...');
                window.location.href = 'dashboard.html';
            }
            
            // Gestionnaire de soumission du formulaire
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
        });

        // Gestionnaire de connexion
        async function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!email || !password) {
                showAlert('Veuillez remplir tous les champs', 'danger');
                return;
            }

            setLoading(true);

            try {
                const response = await axios.post(`${API_URL}/auth/login`, {
                    email: email,
                    motDePasse: password
                });

                if (response.data.success && response.data.token) {
                    // Sauvegarder les informations de connexion
                    localStorage.setItem('authToken', response.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(response.data.utilisateur));

                    if (rememberMe) {
                        localStorage.setItem('rememberMe', 'true');
                    }

                    showAlert(`Connexion réussie ! Bienvenue ${response.data.utilisateur.prenom}`, 'success');

                    // Redirection vers le dashboard
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);

                } else {
                    showAlert('Email ou mot de passe incorrect', 'danger');
                }

            } catch (error) {
                console.error('Erreur de connexion:', error);
                showAlert('Erreur de connexion. Vérifiez vos identifiants.', 'danger');
            } finally {
                setLoading(false);
            }
        }

        // Connexion rapide avec compte de démonstration
        async function quickLogin(email, password, role) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;

            showAlert(`Connexion en tant que ${role}...`, 'info');

            setLoading(true);

            try {
                const response = await axios.post(`${API_URL}/auth/login`, {
                    email: email,
                    motDePasse: password
                });

                if (response.data.success && response.data.token) {
                    localStorage.setItem('authToken', response.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(response.data.utilisateur));

                    showAlert(`Connexion ${role} réussie ! Bienvenue ${response.data.utilisateur.prenom}`, 'success');

                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);

                } else {
                    showAlert('Erreur de connexion', 'danger');
                }

            } catch (error) {
                console.error('Erreur de connexion rapide:', error);
                showAlert('Erreur de connexion. Vérifiez que l\'API est démarrée.', 'danger');
            } finally {
                setLoading(false);
            }
        }

        // Affichage des alertes
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');

            const alertElement = document.createElement('div');
            alertElement.className = `alert alert-${type} alert-dismissible fade show`;
            alertElement.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertElement);

            // Auto-dismiss après 5 secondes
            setTimeout(() => {
                if (alertElement.parentElement) {
                    alertElement.remove();
                }
            }, 5000);
        }

        // Gestion de l'état de chargement
        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginBtnSpinner = document.getElementById('loginBtnSpinner');

            if (loading) {
                loginBtn.disabled = true;
                loginBtnText.classList.add('d-none');
                loginBtnSpinner.classList.remove('d-none');
            } else {
                loginBtn.disabled = false;
                loginBtnText.classList.remove('d-none');
                loginBtnSpinner.classList.add('d-none');
            }
        }

        // Fonctions des liens du footer
        function showHelp() {
            showAlert('Aide : Utilisez les comptes de démonstration pour tester l\'application', 'info');
        }

        function showContact() {
            showAlert('Contact : <EMAIL>', 'info');
        }

        function showPrivacy() {
            showAlert('Politique de confidentialité : Vos données sont protégées selon le RGPD', 'info');
        }

        console.log('🔐 Page de connexion - Prête !');
    </script>
</body>
</html>
