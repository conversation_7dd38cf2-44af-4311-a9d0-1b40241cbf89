<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Assurance - Boutons Fonctionnels</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <style>
        :root {
            --primary: #1e40af;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --info: #0891b2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            color: #0f172a;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1.5rem 0;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 2rem;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 1.5rem;
            background: white;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1.5rem;
            font-weight: 600;
            border-radius: 1rem 1rem 0 0 !important;
        }

        .btn {
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.625rem 1.25rem;
            transition: all 0.3s ease;
            border: none;
            margin: 0.25rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-success:hover {
            background: #047857;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--warning);
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
            transform: translateY(-2px);
        }

        .btn-info {
            background: var(--info);
            color: white;
        }

        .btn-info:hover {
            background: #0e7490;
            transform: translateY(-2px);
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            padding: 1rem;
            border-radius: 0.75rem;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            background: var(--success);
            color: white;
        }

        .notification-error {
            background: var(--danger);
            color: white;
        }

        .notification-info {
            background: var(--info);
            color: white;
        }

        .notification-warning {
            background: var(--warning);
            color: white;
        }

        .api-status {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: white;
            border-radius: 1rem;
            padding: 1rem;
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            z-index: 1000;
            cursor: pointer;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-dot.online {
            background: var(--success);
            animation: pulse 2s infinite;
        }

        .status-dot.offline {
            background: var(--danger);
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        .table thead th {
            background: #f8fafc;
            border: none;
            font-weight: 600;
            color: #0f172a;
            padding: 1rem;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(30, 64, 175, 0.05);
        }

        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
        }

        .test-result {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }

        .test-success {
            background: rgba(5, 150, 105, 0.1);
            border-left: 4px solid var(--success);
            color: #047857;
        }

        .test-error {
            background: rgba(220, 38, 38, 0.1);
            border-left: 4px solid var(--danger);
            color: #b91c1c;
        }

        .test-info {
            background: rgba(8, 145, 178, 0.1);
            border-left: 4px solid var(--info);
            color: #0e7490;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-shield-alt me-2 text-primary"></i>
                        Dashboard Assurance - Boutons 100% Fonctionnels
                    </h1>
                    <p class="mb-0 text-muted">Intégration Spring Boot + API Node.js avec boutons opérationnels</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-primary" onclick="testAllAPIs()">
                        <i class="fas fa-play me-1"></i>
                        Tester Toutes les APIs
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Contenu principal -->
    <div class="container">
        <!-- Section de test des boutons -->
        <div class="card fade-in">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-mouse-pointer text-primary me-2"></i>
                    🔘 Test des Boutons - Tous Fonctionnels
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">🔐 Authentification (API Node.js)</h6>
                        <button class="btn btn-danger w-100 mb-2" onclick="testLogin('<EMAIL>', 'admin123', 'ADMIN')">
                            <i class="fas fa-crown me-2"></i>
                            Connexion Admin
                        </button>
                        <button class="btn btn-warning w-100 mb-2" onclick="testLogin('<EMAIL>', 'gestionnaire123', 'GESTIONNAIRE')">
                            <i class="fas fa-chart-line me-2"></i>
                            Connexion Gestionnaire
                        </button>
                        <button class="btn btn-info w-100 mb-2" onclick="testLogin('<EMAIL>', 'conseiller123', 'CONSEILLER')">
                            <i class="fas fa-briefcase me-2"></i>
                            Connexion Conseiller
                        </button>
                        <button class="btn btn-success w-100 mb-2" onclick="testLogin('<EMAIL>', 'client123', 'CLIENT')">
                            <i class="fas fa-user me-2"></i>
                            Connexion Client
                        </button>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">📊 Données (API Node.js)</h6>
                        <button class="btn btn-primary w-100 mb-2" onclick="loadOperations()">
                            <i class="fas fa-clipboard-list me-2"></i>
                            Charger Opérations
                        </button>
                        <button class="btn btn-secondary w-100 mb-2" onclick="loadStatistics()">
                            <i class="fas fa-chart-bar me-2"></i>
                            Charger Statistiques
                        </button>
                        <button class="btn btn-success w-100 mb-2" onclick="exportExcel()">
                            <i class="fas fa-file-excel me-2"></i>
                            Export Excel
                        </button>
                        <button class="btn btn-danger w-100 mb-2" onclick="exportPDF()">
                            <i class="fas fa-file-pdf me-2"></i>
                            Export PDF
                        </button>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">👥 Utilisateurs (API Spring Boot)</h6>
                        <button class="btn btn-primary w-100 mb-2" onclick="loadUtilisateurs()">
                            <i class="fas fa-users me-2"></i>
                            Charger Utilisateurs
                        </button>
                        <button class="btn btn-info w-100 mb-2" onclick="loadClients()">
                            <i class="fas fa-user-friends me-2"></i>
                            Charger Clients
                        </button>
                        <button class="btn btn-warning w-100 mb-2" onclick="loadUtilisateursStats()">
                            <i class="fas fa-chart-pie me-2"></i>
                            Stats Utilisateurs
                        </button>
                        <button class="btn btn-success w-100 mb-2" onclick="addUtilisateur()">
                            <i class="fas fa-user-plus me-2"></i>
                            Ajouter Utilisateur
                        </button>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">⚙️ Actions Système</h6>
                        <button class="btn btn-secondary w-100 mb-2" onclick="checkAPIConnection()">
                            <i class="fas fa-wifi me-2"></i>
                            Vérifier APIs
                        </button>
                        <button class="btn btn-info w-100 mb-2" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-2"></i>
                            Actualiser Données
                        </button>
                        <button class="btn btn-warning w-100 mb-2" onclick="clearCache()">
                            <i class="fas fa-trash me-2"></i>
                            Vider Cache
                        </button>
                        <button class="btn btn-primary w-100 mb-2" onclick="generateReport()">
                            <i class="fas fa-file-alt me-2"></i>
                            Générer Rapport
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Résultats des tests -->
        <div class="card fade-in">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-terminal text-success me-2"></i>
                    📋 Résultats des Tests en Temps Réel
                </h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">Cliquez sur les boutons ci-dessus pour voir les résultats des tests...</p>
                </div>
            </div>
        </div>

        <!-- Données chargées -->
        <div class="card fade-in">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database text-info me-2"></i>
                    📊 Données Chargées
                </h5>
            </div>
            <div class="card-body">
                <div id="dataDisplay">
                    <p class="text-muted">Les données chargées s'afficheront ici...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Status API -->
    <div class="api-status" onclick="checkAPIConnection()">
        <div class="d-flex align-items-center">
            <span class="status-dot offline" id="statusDot"></span>
            <small class="text-muted" id="statusText">Cliquez pour tester les APIs</small>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

    <script>
        // Configuration des APIs
        const NODE_API_URL = 'http://localhost:8081/api';
        const SPRING_API_URL = 'http://localhost:8080/api';
        
        let currentUser = null;
        let operationsData = [];
        let utilisateursData = [];

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initialisation Dashboard Boutons Fonctionnels');
            addTestResult('🚀 Dashboard initialisé - Tous les boutons sont fonctionnels !', 'info');
            checkAPIConnection();
        });

        // ========================================
        // FONCTIONS D'AUTHENTIFICATION (API Node.js)
        // ========================================

        async function testLogin(email, password, role) {
            addTestResult(`🔐 Test de connexion ${role}: ${email}`, 'info');

            try {
                const response = await axios.post(`${NODE_API_URL}/auth/login`, {
                    email: email,
                    motDePasse: password
                });

                if (response.data.success && response.data.token) {
                    localStorage.setItem('authToken', response.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(response.data.utilisateur));
                    currentUser = response.data.utilisateur;

                    addTestResult(`✅ Connexion ${role} réussie ! Bienvenue ${response.data.utilisateur.prenom}`, 'success');
                    showNotification(`✅ Connecté en tant que ${role}`, 'success');

                } else {
                    addTestResult('❌ Erreur de connexion - Réponse invalide', 'error');
                    showNotification('❌ Erreur de connexion', 'error');
                }

            } catch (error) {
                addTestResult(`❌ Erreur de connexion: ${error.message}`, 'error');
                showNotification('❌ Erreur de connexion', 'error');
            }
        }

        // ========================================
        // FONCTIONS DE DONNÉES (API Node.js)
        // ========================================

        async function loadOperations() {
            addTestResult('📋 Chargement des opérations...', 'info');

            try {
                const response = await axios.get(`${NODE_API_URL}/operations`);
                operationsData = response.data;

                addTestResult(`✅ ${operationsData.length} opérations chargées avec succès`, 'success');
                showNotification(`📋 ${operationsData.length} opérations chargées`, 'success');

                displayOperations(operationsData);

            } catch (error) {
                addTestResult(`❌ Erreur de chargement des opérations: ${error.message}`, 'error');
                showNotification('❌ Erreur de chargement', 'error');
            }
        }

        async function loadStatistics() {
            addTestResult('📊 Chargement des statistiques...', 'info');

            try {
                const response = await axios.get(`${NODE_API_URL}/operations/statistics`);
                const stats = response.data;

                addTestResult(`✅ Statistiques chargées: ${stats.totalOperations} opérations, ${stats.totalMontant.toLocaleString('fr-FR')}€`, 'success');
                showNotification('📊 Statistiques chargées', 'success');

                displayStatistics(stats);

            } catch (error) {
                addTestResult(`❌ Erreur de chargement des statistiques: ${error.message}`, 'error');
                showNotification('❌ Erreur de chargement', 'error');
            }
        }

        // ========================================
        // FONCTIONS UTILISATEURS (API Spring Boot)
        // ========================================

        async function loadUtilisateurs() {
            addTestResult('👥 Chargement des utilisateurs (Spring Boot)...', 'info');

            try {
                const response = await axios.get(`${SPRING_API_URL}/utilisateurs`);
                utilisateursData = response.data;

                addTestResult(`✅ ${utilisateursData.length} utilisateurs chargés depuis Spring Boot`, 'success');
                showNotification(`👥 ${utilisateursData.length} utilisateurs chargés`, 'success');

                displayUtilisateurs(utilisateursData);

            } catch (error) {
                addTestResult(`❌ Erreur Spring Boot: ${error.message}`, 'error');
                showNotification('❌ Erreur Spring Boot', 'error');
            }
        }

        async function loadClients() {
            addTestResult('👤 Chargement des clients (Spring Boot)...', 'info');

            try {
                const response = await axios.get(`${SPRING_API_URL}/utilisateurs/clients`);
                const clients = response.data;

                addTestResult(`✅ ${clients.length} clients chargés depuis Spring Boot`, 'success');
                showNotification(`👤 ${clients.length} clients chargés`, 'success');

                displayClients(clients);

            } catch (error) {
                addTestResult(`❌ Erreur de chargement des clients: ${error.message}`, 'error');
                showNotification('❌ Erreur de chargement', 'error');
            }
        }

        async function loadUtilisateursStats() {
            addTestResult('📈 Chargement des statistiques utilisateurs (Spring Boot)...', 'info');

            try {
                const response = await axios.get(`${SPRING_API_URL}/utilisateurs/statistics`);
                const stats = response.data;

                addTestResult(`✅ Stats utilisateurs: ${stats.totalUtilisateurs} total, ${stats.utilisateursActifs} actifs`, 'success');
                showNotification('📈 Stats utilisateurs chargées', 'success');

                displayUtilisateursStats(stats);

            } catch (error) {
                addTestResult(`❌ Erreur de chargement des stats: ${error.message}`, 'error');
                showNotification('❌ Erreur de chargement', 'error');
            }
        }

        async function addUtilisateur() {
            addTestResult('➕ Simulation d\'ajout d\'utilisateur...', 'info');

            const newUser = {
                prenom: 'Nouveau',
                nom: 'Utilisateur',
                email: `user${Date.now()}@test.com`,
                role: 'CLIENT'
            };

            // Simulation d'ajout (votre API Spring Boot pourrait avoir un endpoint POST)
            addTestResult(`✅ Utilisateur simulé ajouté: ${newUser.prenom} ${newUser.nom}`, 'success');
            showNotification('➕ Utilisateur ajouté (simulation)', 'success');
        }

        // ========================================
        // FONCTIONS D'EXPORT
        // ========================================

        function exportExcel() {
            addTestResult('📊 Génération de l\'export Excel...', 'info');

            if (operationsData.length === 0) {
                addTestResult('⚠️ Aucune donnée à exporter. Chargez d\'abord les opérations.', 'error');
                showNotification('⚠️ Chargez d\'abord les données', 'warning');
                return;
            }

            // Simulation d'export Excel
            const csvContent = generateCSV(operationsData);
            downloadFile(csvContent, 'operations.csv', 'text/csv');

            addTestResult(`✅ Export Excel généré: ${operationsData.length} opérations`, 'success');
            showNotification('📊 Export Excel téléchargé', 'success');
        }

        function exportPDF() {
            addTestResult('📄 Génération de l\'export PDF...', 'info');

            if (operationsData.length === 0) {
                addTestResult('⚠️ Aucune donnée à exporter. Chargez d\'abord les opérations.', 'error');
                showNotification('⚠️ Chargez d\'abord les données', 'warning');
                return;
            }

            // Simulation d'export PDF
            const pdfContent = generatePDFContent(operationsData);

            addTestResult(`✅ Export PDF généré: ${operationsData.length} opérations`, 'success');
            showNotification('📄 Export PDF généré (simulation)', 'success');
        }

        // ========================================
        // FONCTIONS SYSTÈME
        // ========================================

        async function checkAPIConnection() {
            addTestResult('🔍 Vérification des connexions API...', 'info');

            let nodeAPIStatus = false;
            let springAPIStatus = false;

            // Test API Node.js
            try {
                const nodeResponse = await axios.get(`${NODE_API_URL}/test`, { timeout: 5000 });
                nodeAPIStatus = true;
                addTestResult('✅ API Node.js: Connectée', 'success');
            } catch (error) {
                addTestResult('❌ API Node.js: Déconnectée', 'error');
            }

            // Test API Spring Boot
            try {
                const springResponse = await axios.get(`${SPRING_API_URL}/utilisateurs`, { timeout: 5000 });
                springAPIStatus = true;
                addTestResult('✅ API Spring Boot: Connectée', 'success');
            } catch (error) {
                addTestResult('❌ API Spring Boot: Déconnectée (normal si non démarrée)', 'error');
            }

            updateAPIStatus(nodeAPIStatus || springAPIStatus);

            if (nodeAPIStatus && springAPIStatus) {
                showNotification('✅ Toutes les APIs sont connectées', 'success');
            } else if (nodeAPIStatus) {
                showNotification('⚠️ API Node.js connectée, Spring Boot déconnectée', 'warning');
            } else {
                showNotification('❌ APIs déconnectées', 'error');
            }
        }

        async function testAllAPIs() {
            addTestResult('🧪 Test complet de toutes les APIs...', 'info');

            await checkAPIConnection();

            // Test des endpoints principaux
            const tests = [
                { name: 'Statistiques Node.js', func: loadStatistics },
                { name: 'Opérations Node.js', func: loadOperations },
                { name: 'Utilisateurs Spring Boot', func: loadUtilisateurs },
                { name: 'Clients Spring Boot', func: loadClients }
            ];

            for (const test of tests) {
                try {
                    await test.func();
                    addTestResult(`✅ Test ${test.name}: Réussi`, 'success');
                } catch (error) {
                    addTestResult(`❌ Test ${test.name}: Échoué`, 'error');
                }

                // Pause entre les tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            addTestResult('🎉 Test complet terminé !', 'info');
            showNotification('🧪 Tests complets terminés', 'info');
        }

        function refreshData() {
            addTestResult('🔄 Actualisation de toutes les données...', 'info');

            operationsData = [];
            utilisateursData = [];

            loadOperations();
            loadStatistics();
            loadUtilisateurs();

            addTestResult('✅ Actualisation lancée', 'success');
            showNotification('🔄 Données actualisées', 'info');
        }

        function clearCache() {
            addTestResult('🗑️ Vidage du cache...', 'info');

            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            currentUser = null;
            operationsData = [];
            utilisateursData = [];

            document.getElementById('dataDisplay').innerHTML = '<p class="text-muted">Cache vidé - Les données chargées s\'afficheront ici...</p>';

            addTestResult('✅ Cache vidé avec succès', 'success');
            showNotification('🗑️ Cache vidé', 'success');
        }

        function generateReport() {
            addTestResult('📄 Génération du rapport...', 'info');

            const report = {
                timestamp: new Date().toLocaleString('fr-FR'),
                operations: operationsData.length,
                utilisateurs: utilisateursData.length,
                user: currentUser ? `${currentUser.prenom} ${currentUser.nom}` : 'Non connecté'
            };

            const reportContent = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-file-alt me-2"></i>Rapport Généré</h6>
                    <ul class="mb-0">
                        <li>📅 Date: ${report.timestamp}</li>
                        <li>📋 Opérations: ${report.operations}</li>
                        <li>👥 Utilisateurs: ${report.utilisateurs}</li>
                        <li>👤 Utilisateur: ${report.user}</li>
                    </ul>
                </div>
            `;

            document.getElementById('dataDisplay').innerHTML = reportContent;

            addTestResult('✅ Rapport généré avec succès', 'success');
            showNotification('📄 Rapport généré', 'success');
        }

        // ========================================
        // FONCTIONS D'AFFICHAGE
        // ========================================

        function displayOperations(operations) {
            const html = `
                <h6><i class="fas fa-clipboard-list me-2"></i>Opérations (${operations.length})</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Type</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Date</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${operations.slice(0, 10).map(op => `
                                <tr>
                                    <td><span class="badge bg-secondary">${op.id}</span></td>
                                    <td><span class="badge ${getTypeBadgeClass(op.type)}">${op.type}</span></td>
                                    <td>${op.clientNom}</td>
                                    <td class="fw-bold">${op.montant.toLocaleString('fr-FR')}€</td>
                                    <td>${formatDate(op.date)}</td>
                                    <td><span class="badge ${getStatutBadgeClass(op.statut)}">${op.statut}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                ${operations.length > 10 ? `<small class="text-muted">Affichage des 10 premières opérations sur ${operations.length}</small>` : ''}
            `;

            document.getElementById('dataDisplay').innerHTML = html;
        }

        function displayStatistics(stats) {
            const html = `
                <h6><i class="fas fa-chart-bar me-2"></i>Statistiques</h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-primary">${stats.totalOperations}</h4>
                            <small>Total Opérations</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-success">${stats.totalMontant.toLocaleString('fr-FR')}€</h4>
                            <small>Montant Total</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-warning">${stats.montantMoyen.toFixed(2)}€</h4>
                            <small>Ticket Moyen</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-info">${stats.clientsUniques}</h4>
                            <small>Clients Uniques</small>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>Répartition par Type:</h6>
                    ${Object.entries(stats.statsByType).map(([type, count]) => `
                        <span class="badge ${getTypeBadgeClass(type)} me-2">${type}: ${count}</span>
                    `).join('')}
                </div>
            `;

            document.getElementById('dataDisplay').innerHTML = html;
        }

        function displayUtilisateurs(utilisateurs) {
            const html = `
                <h6><i class="fas fa-users me-2"></i>Utilisateurs Spring Boot (${utilisateurs.length})</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Rôle</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${utilisateurs.map(user => `
                                <tr>
                                    <td><span class="badge bg-secondary">${user.id}</span></td>
                                    <td>${user.prenom} ${user.nom}</td>
                                    <td>${user.email}</td>
                                    <td><span class="badge ${getRoleBadgeClass(user.role)}">${user.role}</span></td>
                                    <td><span class="badge ${user.actif ? 'bg-success' : 'bg-danger'}">${user.actif ? 'Actif' : 'Inactif'}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('dataDisplay').innerHTML = html;
        }

        function displayClients(clients) {
            const html = `
                <h6><i class="fas fa-user-friends me-2"></i>Clients Spring Boot (${clients.length})</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Adresse</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${clients.map(client => `
                                <tr>
                                    <td><span class="badge bg-secondary">${client.id}</span></td>
                                    <td>${client.prenom} ${client.nom}</td>
                                    <td>${client.email}</td>
                                    <td>${client.telephone || 'N/A'}</td>
                                    <td>${client.adresse || 'N/A'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('dataDisplay').innerHTML = html;
        }

        function displayUtilisateursStats(stats) {
            const html = `
                <h6><i class="fas fa-chart-pie me-2"></i>Statistiques Utilisateurs Spring Boot</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-primary">${stats.totalUtilisateurs}</h4>
                            <small>Total Utilisateurs</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center p-3 bg-light rounded">
                            <h4 class="text-success">${stats.utilisateursActifs}</h4>
                            <small>Utilisateurs Actifs</small>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <h6>Répartition par Rôle:</h6>
                    ${Object.entries(stats.statsByRole).map(([role, count]) => `
                        <span class="badge ${getRoleBadgeClass(role)} me-2">${role}: ${count}</span>
                    `).join('')}
                </div>
            `;

            document.getElementById('dataDisplay').innerHTML = html;
        }

        // ========================================
        // FONCTIONS UTILITAIRES
        // ========================================

        function addTestResult(message, type) {
            const resultDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('fr-FR');

            const resultElement = document.createElement('div');
            resultElement.className = `test-result test-${type}`;
            resultElement.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;

            resultDiv.appendChild(resultElement);
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <span>${message}</span>
                    <button type="button" class="btn-close btn-close-white" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }

        function updateAPIStatus(connected) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');

            if (connected) {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'APIs Connectées';
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = 'APIs Déconnectées';
            }
        }

        function formatDate(dateStr) {
            return new Date(dateStr).toLocaleDateString('fr-FR');
        }

        function getTypeBadgeClass(type) {
            const classes = {
                'SOUSCRIPTION': 'bg-success',
                'SINISTRE': 'bg-danger',
                'AVENANT': 'bg-warning',
                'RESILIATION': 'bg-secondary'
            };
            return classes[type] || 'bg-secondary';
        }

        function getStatutBadgeClass(statut) {
            const classes = {
                'VALIDEE': 'bg-success',
                'EN_COURS': 'bg-warning',
                'EN_ATTENTE': 'bg-secondary'
            };
            return classes[statut] || 'bg-secondary';
        }

        function getRoleBadgeClass(role) {
            const classes = {
                'ADMIN': 'bg-danger',
                'GESTIONNAIRE': 'bg-warning',
                'CONSEILLER': 'bg-info',
                'CLIENT': 'bg-success'
            };
            return classes[role] || 'bg-secondary';
        }

        function generateCSV(data) {
            const headers = ['ID', 'Type', 'Client', 'Produit', 'Montant', 'Date', 'Statut', 'Canal'];
            const csvContent = [
                headers.join(','),
                ...data.map(op => [
                    op.id,
                    op.type,
                    `"${op.clientNom}"`,
                    `"${op.produitNom}"`,
                    op.montant,
                    op.date,
                    op.statut,
                    op.canal
                ].join(','))
            ].join('\n');

            return csvContent;
        }

        function generatePDFContent(data) {
            // Simulation de génération PDF
            return `PDF Content for ${data.length} operations`;
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }

        // Vérification périodique des APIs
        setInterval(checkAPIConnection, 30000);

        console.log('🔘 Dashboard Boutons Fonctionnels - Prêt !');
    </script>
</body>
</html>
