<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Assurance - Tableau de Bord</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <style>
        :root {
            --primary: #1e40af;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --info: #0891b2;
            --light: #f8fafc;
            --dark: #0f172a;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light);
            color: var(--dark);
        }

        .navbar {
            background: white !important;
            box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--primary) !important;
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 4px -1px rgb(0 0 0 / 0.1);
            padding: 0;
            position: fixed;
            top: 76px;
            left: 0;
            width: 280px;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: var(--gradient-primary);
            color: white;
        }

        .nav-pills .nav-link {
            color: var(--dark);
            padding: 0.875rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .nav-pills .nav-link:hover {
            background: rgba(30, 64, 175, 0.1);
            color: var(--primary);
        }

        .nav-pills .nav-link.active {
            background: var(--gradient-primary);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: calc(100vh - 76px);
        }

        .kpi-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 1.5rem;
            cursor: pointer;
            border-left: 4px solid var(--primary);
        }

        .kpi-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        }

        .kpi-card.success {
            border-left-color: var(--success);
        }

        .kpi-card.warning {
            border-left-color: var(--warning);
        }

        .kpi-card.danger {
            border-left-color: var(--danger);
        }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }

        .kpi-card.success .kpi-value {
            color: var(--success);
        }

        .kpi-card.warning .kpi-value {
            color: var(--warning);
        }

        .kpi-card.danger .kpi-value {
            color: var(--danger);
        }

        .kpi-label {
            color: #64748b;
            font-weight: 500;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .kpi-icon {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
            background: var(--primary);
        }

        .kpi-card.success .kpi-icon {
            background: var(--success);
        }

        .kpi-card.warning .kpi-icon {
            background: var(--warning);
        }

        .kpi-card.danger .kpi-icon {
            background: var(--danger);
        }

        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 1.5rem;
            background: white;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1.5rem;
            font-weight: 600;
            border-radius: 1rem 1rem 0 0 !important;
        }

        .table thead th {
            background: var(--light);
            border: none;
            font-weight: 600;
            color: var(--dark);
            padding: 1rem;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(30, 64, 175, 0.05);
        }

        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
        }

        .btn {
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.625rem 1.25rem;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container-fluid">
            <button class="btn btn-outline-primary d-lg-none me-3" type="button" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                Dashboard Assurance
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="user-avatar me-2" id="userAvatar">U</div>
                        <span id="userName">Utilisateur</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showProfile()">
                            <i class="fas fa-user me-2"></i>Profil
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()">
                            <i class="fas fa-cog me-2"></i>Paramètres
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="user-info" id="userInfo">
                <div class="user-avatar" id="sidebarAvatar">U</div>
                <div>
                    <div class="fw-bold" id="sidebarUserName">Utilisateur</div>
                    <small id="sidebarUserRole">Rôle</small>
                </div>
            </div>
        </div>
        
        <nav class="nav nav-pills flex-column mt-3">
            <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                <i class="fas fa-tachometer-alt"></i>
                Vue d'ensemble
            </a>
            <a class="nav-link" href="#" onclick="showSection('operations')">
                <i class="fas fa-clipboard-list"></i>
                Opérations
            </a>
            <a class="nav-link" href="#" onclick="showSection('utilisateurs')">
                <i class="fas fa-users"></i>
                Utilisateurs
            </a>
            <a class="nav-link" href="#" onclick="showSection('clients')">
                <i class="fas fa-user-friends"></i>
                Clients
            </a>
            <a class="nav-link" href="#" onclick="showSection('rapports')">
                <i class="fas fa-chart-bar"></i>
                Rapports
            </a>
            <a class="nav-link" href="#" onclick="showSection('parametres')">
                <i class="fas fa-cog"></i>
                Paramètres
            </a>
        </nav>
    </div>

    <!-- Contenu principal -->
    <div class="main-content">
        <div id="mainContent">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2">Chargement du dashboard...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

    <script>
        // Configuration des APIs
        const NODE_API_URL = 'http://localhost:8081/api';
        const SPRING_API_URL = 'http://localhost:8080/api';
        
        let currentUser = null;
        let currentSection = 'dashboard';
        let charts = {};
        let dataTable = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 Initialisation Dashboard Professionnel');
            
            // Vérifier l'authentification
            checkAuthentication();
            
            // Charger les données initiales
            loadDashboardData();
        });

        // Vérification de l'authentification
        function checkAuthentication() {
            const authToken = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');

            if (!authToken || !userInfo) {
                console.log('Utilisateur non connecté, redirection vers login');
                window.location.href = 'login.html';
                return;
            }

            try {
                currentUser = JSON.parse(userInfo);
                updateUserInterface();
                console.log('Utilisateur connecté:', currentUser);
            } catch (error) {
                console.error('Erreur de parsing des données utilisateur:', error);
                logout();
            }
        }

        // Mise à jour de l'interface utilisateur
        function updateUserInterface() {
            if (!currentUser) return;

            const initials = (currentUser.prenom.charAt(0) + currentUser.nom.charAt(0)).toUpperCase();
            const fullName = `${currentUser.prenom} ${currentUser.nom}`;

            // Mise à jour de la navbar
            document.getElementById('userAvatar').textContent = initials;
            document.getElementById('userName').textContent = fullName;

            // Mise à jour de la sidebar
            document.getElementById('sidebarAvatar').textContent = initials;
            document.getElementById('sidebarUserName').textContent = fullName;
            document.getElementById('sidebarUserRole').textContent = currentUser.role || 'Utilisateur';
        }

        // Chargement des données du dashboard
        async function loadDashboardData() {
            try {
                console.log('📊 Chargement des données du dashboard...');

                // Chargement parallèle des données
                const [statsResponse, operationsResponse] = await Promise.all([
                    axios.get(`${NODE_API_URL}/operations/statistics`),
                    axios.get(`${NODE_API_URL}/operations`)
                ]);

                const statistics = statsResponse.data;
                const operations = operationsResponse.data;

                // Affichage du dashboard principal
                showDashboardSection(statistics, operations);

                console.log('✅ Données du dashboard chargées');

            } catch (error) {
                console.error('❌ Erreur de chargement:', error);
                showErrorSection();
            }
        }

        // Affichage de la section dashboard
        function showDashboardSection(statistics, operations) {
            const content = `
                <!-- En-tête de section -->
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h2 class="h4 fw-bold mb-1">📊 Vue d'ensemble</h2>
                        <p class="text-muted mb-0">Tableau de bord analytique en temps réel</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-success btn-sm" onclick="exportExcel()">
                            <i class="fas fa-file-excel me-1"></i>
                            Excel
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="exportPDF()">
                            <i class="fas fa-file-pdf me-1"></i>
                            PDF
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser
                        </button>
                    </div>
                </div>

                <!-- KPI Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="kpi-card fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="kpi-value">${statistics.totalOperations?.toLocaleString('fr-FR') || '0'}</div>
                            <div class="kpi-label">Total Opérations</div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="kpi-card success fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="kpi-value">${formatCurrency(statistics.totalMontant || 0)}</div>
                            <div class="kpi-label">Chiffre d'Affaires</div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="kpi-card warning fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="kpi-value">${formatCurrency(statistics.montantMoyen || 0)}</div>
                            <div class="kpi-label">Ticket Moyen</div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="kpi-card danger fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="kpi-value">${statistics.clientsUniques || '0'}</div>
                            <div class="kpi-label">Clients Actifs</div>
                        </div>
                    </div>
                </div>

                <!-- Graphiques -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card fade-in">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line text-primary me-2"></i>
                                    📈 Évolution des Opérations
                                </h5>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary active" onclick="changeChartPeriod('7d')">7J</button>
                                    <button class="btn btn-outline-primary" onclick="changeChartPeriod('30d')">30J</button>
                                    <button class="btn btn-outline-primary" onclick="changeChartPeriod('90d')">90J</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="evolutionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card fade-in">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie text-success me-2"></i>
                                    🥧 Répartition par Type
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="typeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tableau des opérations -->
                <div class="card fade-in">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table text-info me-2"></i>
                            📊 Opérations Récentes (${operations.length} opérations)
                        </h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-primary" onclick="addOperation()">
                                <i class="fas fa-plus me-1"></i>
                                Ajouter
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="filterOperations()">
                                <i class="fas fa-filter me-1"></i>
                                Filtrer
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="operationsTable">
                                <thead>
                                    <tr>
                                        <th>🆔 ID</th>
                                        <th>📋 Type</th>
                                        <th>👤 Client</th>
                                        <th>📦 Produit</th>
                                        <th>💰 Montant</th>
                                        <th>📅 Date</th>
                                        <th>📊 Statut</th>
                                        <th>📡 Canal</th>
                                        <th>⚙️ Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${generateOperationsRows(operations)}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('mainContent').innerHTML = content;

            // Initialiser les graphiques et le tableau
            setTimeout(() => {
                initializeCharts(statistics);
                initializeDataTable();
            }, 100);
        }

        // Navigation entre sections
        function showSection(section) {
            currentSection = section;

            // Mise à jour de la navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');

            // Chargement du contenu selon la section
            switch(section) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'operations':
                    loadOperationsSection();
                    break;
                case 'utilisateurs':
                    loadUtilisateursSection();
                    break;
                case 'clients':
                    loadClientsSection();
                    break;
                case 'rapports':
                    loadRapportsSection();
                    break;
                case 'parametres':
                    loadParametresSection();
                    break;
                default:
                    loadDashboardData();
            }
        }

        // Section Utilisateurs (intégration Spring Boot)
        async function loadUtilisateursSection() {
            document.getElementById('mainContent').innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h2 class="h4 fw-bold mb-1">👥 Gestion des Utilisateurs</h2>
                        <p class="text-muted mb-0">Intégration avec votre contrôleur Spring Boot</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-sm" onclick="loadUtilisateursFromSpring()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Charger depuis Spring Boot
                        </button>
                        <button class="btn btn-success btn-sm" onclick="addUtilisateur()">
                            <i class="fas fa-user-plus me-1"></i>
                            Ajouter Utilisateur
                        </button>
                    </div>
                </div>

                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users text-primary me-2"></i>
                            Liste des Utilisateurs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="utilisateursContent">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Chargement...</span>
                                </div>
                                <p class="mt-2">Chargement des utilisateurs...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Charger automatiquement les utilisateurs
            loadUtilisateursFromSpring();
        }

        // Chargement des utilisateurs depuis Spring Boot
        async function loadUtilisateursFromSpring() {
            try {
                console.log('👥 Chargement des utilisateurs depuis Spring Boot...');

                const response = await axios.get(`${SPRING_API_URL}/utilisateurs`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                    }
                });

                const utilisateurs = response.data;
                displayUtilisateurs(utilisateurs);

                console.log(`✅ ${utilisateurs.length} utilisateurs chargés depuis Spring Boot`);

            } catch (error) {
                console.error('❌ Erreur de chargement des utilisateurs:', error);
                document.getElementById('utilisateursContent').innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">Erreur de connexion Spring Boot</h5>
                        <p class="text-muted">Impossible de charger les utilisateurs depuis votre API Spring Boot.</p>
                        <p class="text-muted">Vérifiez que votre serveur Spring Boot est démarré sur le port 8080.</p>
                        <button class="btn btn-primary" onclick="loadUtilisateursFromSpring()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Réessayer
                        </button>
                    </div>
                `;
            }
        }

        // Affichage des utilisateurs
        function displayUtilisateurs(utilisateurs) {
            const html = `
                <div class="table-responsive">
                    <table class="table table-hover" id="utilisateursTable">
                        <thead>
                            <tr>
                                <th>🆔 ID</th>
                                <th>👤 Nom</th>
                                <th>📧 Email</th>
                                <th>🎭 Rôle</th>
                                <th>📅 Créé le</th>
                                <th>📊 Statut</th>
                                <th>⚙️ Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${utilisateurs.map(user => `
                                <tr>
                                    <td><span class="badge bg-secondary">${user.id}</span></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-2" style="width: 32px; height: 32px; font-size: 0.75rem;">
                                                ${(user.prenom.charAt(0) + user.nom.charAt(0)).toUpperCase()}
                                            </div>
                                            ${user.prenom} ${user.nom}
                                        </div>
                                    </td>
                                    <td>${user.email}</td>
                                    <td><span class="badge ${getRoleBadgeClass(user.role)}">${user.role}</span></td>
                                    <td>${formatDate(user.dateCreation || new Date())}</td>
                                    <td><span class="badge ${user.actif ? 'bg-success' : 'bg-danger'}">${user.actif ? 'Actif' : 'Inactif'}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewUtilisateur(${user.id})" title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editUtilisateur(${user.id})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteUtilisateur(${user.id})" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>${utilisateurs.length} utilisateurs</strong> chargés depuis votre contrôleur Spring Boot
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Intégration réussie</strong> avec UtilisateurController.java
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('utilisateursContent').innerHTML = html;

            // Initialiser DataTable pour les utilisateurs
            setTimeout(() => {
                if ($.fn.DataTable.isDataTable('#utilisateursTable')) {
                    $('#utilisateursTable').DataTable().destroy();
                }

                $('#utilisateursTable').DataTable({
                    responsive: true,
                    pageLength: 10,
                    language: {
                        url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json'
                    },
                    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                         '<"row"<"col-sm-12"tr>>' +
                         '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                    columnDefs: [
                        { orderable: false, targets: -1 }
                    ]
                });
            }, 100);
        }

        // Fonctions utilitaires
        function formatCurrency(amount) {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount || 0);
        }

        function formatDate(dateStr) {
            return new Date(dateStr).toLocaleDateString('fr-FR');
        }

        function getRoleBadgeClass(role) {
            const classes = {
                'ADMIN': 'bg-danger',
                'GESTIONNAIRE': 'bg-warning',
                'CONSEILLER': 'bg-info',
                'CLIENT': 'bg-success'
            };
            return classes[role] || 'bg-secondary';
        }

        function generateOperationsRows(operations) {
            if (!operations || operations.length === 0) {
                return '<tr><td colspan="9" class="text-center text-muted">Aucune opération trouvée</td></tr>';
            }

            return operations.slice(0, 20).map(op => `
                <tr>
                    <td><span class="badge bg-secondary">${op.id}</span></td>
                    <td><span class="badge ${getTypeBadgeClass(op.type)}">${op.type}</span></td>
                    <td>${op.clientNom}</td>
                    <td>${op.produitNom}</td>
                    <td class="fw-bold">${op.montant.toLocaleString('fr-FR')}€</td>
                    <td>${formatDate(op.date)}</td>
                    <td><span class="badge ${getStatutBadgeClass(op.statut)}">${op.statut}</span></td>
                    <td><span class="badge bg-info">${op.canal}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewOperation(${op.id})" title="Voir">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editOperation(${op.id})" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteOperation(${op.id})" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function getTypeBadgeClass(type) {
            const classes = {
                'SOUSCRIPTION': 'bg-success',
                'SINISTRE': 'bg-danger',
                'AVENANT': 'bg-warning',
                'RESILIATION': 'bg-secondary'
            };
            return classes[type] || 'bg-secondary';
        }

        function getStatutBadgeClass(statut) {
            const classes = {
                'VALIDEE': 'bg-success',
                'EN_COURS': 'bg-warning',
                'EN_ATTENTE': 'bg-secondary'
            };
            return classes[statut] || 'bg-secondary';
        }

        // Fonctions d'action
        function animateCard(card) {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
        }

        function refreshData() {
            console.log('🔄 Actualisation des données...');
            loadDashboardData();
        }

        function exportExcel() {
            console.log('📊 Export Excel...');
            alert('Export Excel en cours de développement');
        }

        function exportPDF() {
            console.log('📄 Export PDF...');
            alert('Export PDF en cours de développement');
        }

        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            localStorage.removeItem('rememberMe');
            window.location.href = 'login.html';
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Fonctions de sections (placeholders)
        function loadOperationsSection() {
            document.getElementById('mainContent').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list text-primary" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">Gestion des Opérations</h3>
                    <p class="text-muted">Module de gestion des opérations d'assurance</p>
                    <button class="btn btn-primary" onclick="showSection('dashboard')">
                        Retour au Dashboard
                    </button>
                </div>
            `;
        }

        function loadClientsSection() {
            document.getElementById('mainContent').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-user-friends text-success" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">Gestion des Clients</h3>
                    <p class="text-muted">Module de gestion de la clientèle</p>
                    <button class="btn btn-primary" onclick="showSection('dashboard')">
                        Retour au Dashboard
                    </button>
                </div>
            `;
        }

        function loadRapportsSection() {
            document.getElementById('mainContent').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-chart-bar text-warning" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">Rapports et Analyses</h3>
                    <p class="text-muted">Module de génération de rapports</p>
                    <button class="btn btn-primary" onclick="showSection('dashboard')">
                        Retour au Dashboard
                    </button>
                </div>
            `;
        }

        function loadParametresSection() {
            document.getElementById('mainContent').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-cog text-secondary" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">Paramètres</h3>
                    <p class="text-muted">Configuration de l'application</p>
                    <button class="btn btn-primary" onclick="showSection('dashboard')">
                        Retour au Dashboard
                    </button>
                </div>
            `;
        }

        function showErrorSection() {
            document.getElementById('mainContent').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">Erreur de connexion</h3>
                    <p class="text-muted">Impossible de charger les données</p>
                    <button class="btn btn-primary" onclick="loadDashboardData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Réessayer
                    </button>
                </div>
            `;
        }

        // Fonctions d'action sur les entités (placeholders)
        function viewOperation(id) { alert(`Voir opération ${id}`); }
        function editOperation(id) { alert(`Modifier opération ${id}`); }
        function deleteOperation(id) { alert(`Supprimer opération ${id}`); }
        function addOperation() { alert('Ajouter une opération'); }
        function filterOperations() { alert('Filtrer les opérations'); }
        function viewUtilisateur(id) { alert(`Voir utilisateur ${id}`); }
        function editUtilisateur(id) { alert(`Modifier utilisateur ${id}`); }
        function deleteUtilisateur(id) { alert(`Supprimer utilisateur ${id}`); }
        function addUtilisateur() { alert('Ajouter un utilisateur'); }
        function showProfile() { alert('Profil utilisateur'); }
        function showSettings() { alert('Paramètres'); }
        function changeChartPeriod(period) { console.log('Période:', period); }

        // Initialisation des graphiques et DataTable (placeholders)
        function initializeCharts(statistics) {
            console.log('📈 Initialisation des graphiques avec:', statistics);
        }

        function initializeDataTable() {
            console.log('📊 Initialisation DataTable');
        }

        console.log('📊 Dashboard Professionnel - Prêt !');
    </script>
</body>
</html>
