/* Variables CSS personnalisées */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --dark-bg: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* Styles généraux */
body {
    background-color: var(--light-bg);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Sidebar */
.sidebar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 5px 0;
    border-radius: 8px;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* Contenu principal */
.main-content {
    padding: 20px;
    margin-left: 16.66667%; /* Largeur de la sidebar */
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 20px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    padding: 15px 20px;
    border-bottom: none;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* KPI Cards */
.kpi-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 20px;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
    pointer-events: none;
}

.kpi-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.kpi-card.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.kpi-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.kpi-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.kpi-card.danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.kpi-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Section de filtres */
.filter-section {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--box-shadow);
}

.filter-section h5 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 600;
}

/* Boutons */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-outline-secondary {
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* Graphiques */
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
    padding: 10px;
}

/* Tables */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5em 0.8em;
    border-radius: 20px;
    font-weight: 500;
}

.badge.rachat {
    background-color: var(--danger-color);
    color: white;
}

.badge.versement {
    background-color: var(--success-color);
    color: white;
}

.badge.arbitrage {
    background-color: var(--warning-color);
    color: white;
}

/* Loading */
.loading {
    display: none;
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.spinner-border {
    color: var(--secondary-color);
    width: 3rem;
    height: 3rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Responsive */
@media (max-width: 768px) {
    .kpi-value {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .filter-section {
        padding: 15px;
    }
    
    .card-header {
        padding: 12px 15px;
    }
}

/* DataTables personnalisations */
.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color)) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    border-radius: 5px !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--secondary-color) !important;
    border-color: var(--secondary-color) !important;
    color: white !important;
    border-radius: 5px !important;
}

.dataTables_wrapper .dataTables_filter input {
    border-radius: 20px;
    border: 2px solid var(--secondary-color);
    padding: 8px 15px;
}

.dataTables_wrapper .dataTables_length select {
    border-radius: 20px;
    border: 2px solid var(--secondary-color);
    padding: 5px 10px;
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
