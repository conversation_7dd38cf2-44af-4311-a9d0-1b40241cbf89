<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Dashboard Assurance Pro</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --light-color: #f8fafc;
            --dark-color: #0f172a;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--dark-color);
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 2rem;
            padding: 3rem;
            box-shadow: var(--shadow-lg);
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .login-logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin: 0 auto 1.5rem;
            box-shadow: var(--shadow);
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: var(--secondary-color);
            font-size: 1rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
        }

        .btn-login {
            width: 100%;
            padding: 1rem;
            background: var(--gradient-primary);
            border: none;
            border-radius: 0.75rem;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .demo-accounts {
            background: var(--light-color);
            border-radius: 1rem;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .demo-accounts-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--secondary-color);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 1rem;
            text-align: center;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 0.75rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .demo-account:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .demo-account:last-child {
            margin-bottom: 0;
        }

        .demo-account-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .demo-account-icon {
            width: 40px;
            height: 40px;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.125rem;
        }

        .demo-account-icon.admin {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }

        .demo-account-icon.gestionnaire {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        }

        .demo-account-icon.conseiller {
            background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
        }

        .demo-account-icon.client {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        .demo-account-text {
            flex: 1;
        }

        .demo-account-name {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.875rem;
        }

        .demo-account-role {
            color: var(--secondary-color);
            font-size: 0.75rem;
        }

        .badge {
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
        }

        .badge-admin {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
        }

        .badge-gestionnaire {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            color: white;
        }

        .badge-conseiller {
            background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            color: white;
        }

        .badge-client {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
        }

        .alert {
            border: none;
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .alert-danger {
            background: rgba(220, 38, 38, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(220, 38, 38, 0.2);
        }

        .alert-success {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(5, 150, 105, 0.2);
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .api-status {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: white;
            border-radius: 1rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-dot.online {
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        .status-dot.offline {
            background: var(--danger-color);
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .api-status {
                top: 1rem;
                right: 1rem;
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Status API -->
    <div class="api-status">
        <span class="status-dot offline" id="statusDot"></span>
        <small class="text-muted" id="statusText">Vérification API...</small>
    </div>

    <!-- Container de connexion -->
    <div class="login-container fade-in">
        <div class="login-header">
            <div class="login-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h1 class="login-title">Connexion</h1>
            <p class="login-subtitle">Dashboard Assurance Professionnel</p>
        </div>

        <form id="loginForm">
            <div class="form-floating">
                <input type="email" class="form-control" id="email" placeholder="Email" required>
                <label for="email">
                    <i class="fas fa-envelope me-2"></i>
                    Adresse email
                </label>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" placeholder="Mot de passe" required>
                <label for="password">
                    <i class="fas fa-lock me-2"></i>
                    Mot de passe
                </label>
            </div>

            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    Se souvenir de moi
                </label>
            </div>

            <button type="submit" class="btn-login" id="loginBtn">
                <span id="loginText">Se connecter</span>
                <span id="loginSpinner" style="display: none;">
                    <span class="loading-spinner"></span>
                    Connexion...
                </span>
            </button>
        </form>

        <div id="loginAlert" style="display: none;"></div>

        <div class="demo-accounts">
            <div class="demo-accounts-title">
                🎯 Comptes de démonstration
            </div>

            <div class="demo-account" onclick="fillLogin('<EMAIL>', 'admin123')">
                <div class="demo-account-info">
                    <div class="demo-account-icon admin">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="demo-account-text">
                        <div class="demo-account-name">Administrateur</div>
                        <div class="demo-account-role">Accès complet au système</div>
                    </div>
                </div>
                <span class="badge badge-admin">ADMIN</span>
            </div>

            <div class="demo-account" onclick="fillLogin('<EMAIL>', 'gestionnaire123')">
                <div class="demo-account-info">
                    <div class="demo-account-icon gestionnaire">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="demo-account-text">
                        <div class="demo-account-name">Gestionnaire</div>
                        <div class="demo-account-role">Gestion des opérations</div>
                    </div>
                </div>
                <span class="badge badge-gestionnaire">GESTIONNAIRE</span>
            </div>

            <div class="demo-account" onclick="fillLogin('<EMAIL>', 'conseiller123')">
                <div class="demo-account-info">
                    <div class="demo-account-icon conseiller">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="demo-account-text">
                        <div class="demo-account-name">Conseiller</div>
                        <div class="demo-account-role">Conseil clientèle</div>
                    </div>
                </div>
                <span class="badge badge-conseiller">CONSEILLER</span>
            </div>

            <div class="demo-account" onclick="fillLogin('<EMAIL>', 'client123')">
                <div class="demo-account-info">
                    <div class="demo-account-icon client">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="demo-account-text">
                        <div class="demo-account-name">Client</div>
                        <div class="demo-account-role">Espace personnel</div>
                    </div>
                </div>
                <span class="badge badge-client">CLIENT</span>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:8081/api';

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔐 Initialisation de la page de connexion');
            setupLoginForm();
            checkAPIConnection();
        });

        // Vérification de l'API
        async function checkAPIConnection() {
            try {
                const response = await axios.get(`${API_BASE_URL}/test`, { timeout: 5000 });
                updateAPIStatus(true);
                console.log('✅ API connectée');
            } catch (error) {
                updateAPIStatus(false);
                console.error('❌ API déconnectée:', error.message);
            }
        }

        // Mise à jour du statut API
        function updateAPIStatus(connected) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (connected) {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'API Connectée';
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = 'API Déconnectée';
            }
        }

        // Configuration du formulaire
        function setupLoginForm() {
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);
        }

        // Gestion de la connexion
        async function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginSpinner = document.getElementById('loginSpinner');
            
            // Afficher le spinner
            loginBtn.disabled = true;
            loginText.style.display = 'none';
            loginSpinner.style.display = 'inline-flex';
            hideAlert();
            
            try {
                const response = await axios.post(`${API_BASE_URL}/auth/login`, {
                    email: email,
                    motDePasse: password
                }, { timeout: 10000 });
                
                if (response.data.success && response.data.token) {
                    // Stocker les informations
                    localStorage.setItem('authToken', response.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(response.data.utilisateur));
                    
                    showAlert('Connexion réussie ! Redirection...', 'success');
                    
                    // Redirection vers le dashboard
                    setTimeout(() => {
                        window.location.href = 'dashboard-professionnel.html';
                    }, 1500);
                    
                } else {
                    showAlert('Erreur de connexion - Réponse invalide', 'danger');
                }
                
            } catch (error) {
                console.error('Erreur de connexion:', error);
                
                if (error.response && error.response.status === 401) {
                    showAlert('Email ou mot de passe incorrect', 'danger');
                } else if (error.code === 'ECONNREFUSED' || error.message.includes('Network Error')) {
                    showAlert('Impossible de se connecter à l\'API. Vérifiez que le serveur est démarré.', 'danger');
                } else {
                    showAlert('Erreur de connexion : ' + error.message, 'danger');
                }
            } finally {
                // Masquer le spinner
                loginBtn.disabled = false;
                loginText.style.display = 'inline';
                loginSpinner.style.display = 'none';
            }
        }

        // Remplir automatiquement les champs
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            showAlert('Champs remplis automatiquement - Cliquez sur "Se connecter"', 'success');
        }

        // Afficher une alerte
        function showAlert(message, type) {
            const alertDiv = document.getElementById('loginAlert');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>${message}`;
            alertDiv.style.display = 'block';
        }

        // Masquer l'alerte
        function hideAlert() {
            document.getElementById('loginAlert').style.display = 'none';
        }

        console.log('🔐 Page de connexion professionnelle prête !');
    </script>
</body>
</html>
