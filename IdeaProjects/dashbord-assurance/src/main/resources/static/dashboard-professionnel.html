<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Assurance Professionnel</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
    
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-color: #f8fafc;
            --dark-color: #0f172a;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            --gradient-warning: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            --gradient-danger: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* Header professionnel */
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }

        .header .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .header .nav-link:hover {
            color: white !important;
            transform: translateY(-1px);
        }

        /* Sidebar professionnel */
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .sidebar-nav {
            padding: 1.5rem 0;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            color: var(--secondary-color);
            padding: 0.875rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0 1rem;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .nav-link:hover, .nav-link.active {
            background: var(--gradient-primary);
            color: white;
            transform: translateX(4px);
            box-shadow: var(--shadow);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Cards professionnelles */
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .card-body {
            padding: 1.5rem;
        }

        /* KPI Cards */
        .kpi-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .kpi-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--shadow-lg);
        }

        .kpi-card.success::before {
            background: var(--gradient-success);
        }

        .kpi-card.warning::before {
            background: var(--gradient-warning);
        }

        .kpi-card.danger::before {
            background: var(--gradient-danger);
        }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .kpi-card.success .kpi-value {
            background: var(--gradient-success);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .kpi-card.warning .kpi-value {
            background: var(--gradient-warning);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .kpi-card.danger .kpi-value {
            background: var(--gradient-danger);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .kpi-label {
            color: var(--secondary-color);
            font-weight: 500;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .kpi-icon {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
            background: var(--gradient-primary);
        }

        .kpi-card.success .kpi-icon {
            background: var(--gradient-success);
        }

        .kpi-card.warning .kpi-icon {
            background: var(--gradient-warning);
        }

        .kpi-card.danger .kpi-icon {
            background: var(--gradient-danger);
        }

        /* Charts */
        .chart-container {
            position: relative;
            height: 400px;
            padding: 1rem;
        }

        /* Tables professionnelles */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background: var(--light-color);
            border: none;
            font-weight: 600;
            color: var(--dark-color);
            padding: 1rem;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        .table tbody td {
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        /* Badges professionnels */
        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
        }

        .badge-success {
            background: var(--gradient-success);
            color: white;
        }

        .badge-warning {
            background: var(--gradient-warning);
            color: white;
        }

        .badge-danger {
            background: var(--gradient-danger);
            color: white;
        }

        .badge-info {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            color: white;
        }

        /* Boutons professionnels */
        .btn {
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.625rem 1.25rem;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .btn-success {
            background: var(--gradient-success);
            color: white;
        }

        .btn-warning {
            background: var(--gradient-warning);
            color: white;
        }

        .btn-danger {
            background: var(--gradient-danger);
            color: white;
        }

        /* Animations */
        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                top: 76px;
                width: 280px;
                z-index: 999;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                margin-left: 0 !important;
            }

            .kpi-value {
                font-size: 2rem;
            }
        }

        /* Status indicators */
        .status-indicator {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: white;
            border-radius: 1rem;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-dot.online {
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        .status-dot.offline {
            background: var(--danger-color);
        }

        /* Loading states */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Header professionnel -->
    <header class="header">
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-shield-alt me-2"></i>
                    AssuranceBoard Pro
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#operations">
                                <i class="fas fa-clipboard-list me-1"></i>
                                Opérations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#clients">
                                <i class="fas fa-users me-1"></i>
                                Clients
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#rapports">
                                <i class="fas fa-chart-bar me-1"></i>
                                Rapports
                            </a>
                        </li>
                    </ul>
                    
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>
                                <span id="userName">Utilisateur</span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#profile"><i class="fas fa-user me-2"></i>Profil</a></li>
                                <li><a class="dropdown-item" href="#settings"><i class="fas fa-cog me-2"></i>Paramètres</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar professionnel -->
            <nav class="col-md-3 col-lg-2 sidebar">
                <div class="sidebar-nav">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                                <i class="fas fa-tachometer-alt"></i>
                                Vue d'ensemble
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('operations')">
                                <i class="fas fa-clipboard-list"></i>
                                Opérations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('utilisateurs')">
                                <i class="fas fa-users"></i>
                                Utilisateurs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('produits')">
                                <i class="fas fa-box"></i>
                                Produits
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('rapports')">
                                <i class="fas fa-chart-bar"></i>
                                Rapports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('parametres')">
                                <i class="fas fa-cog"></i>
                                Paramètres
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Contenu principal -->
            <main class="col-md-9 col-lg-10 main-content" style="margin-left: 16.666667%;">
                <div class="container-fluid py-4">
                    <div id="dashboardContent">
                        <!-- Le contenu sera chargé ici -->
                        <div class="loading">
                            <div class="spinner"></div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Status indicator -->
    <div class="status-indicator">
        <div class="d-flex align-items-center">
            <span class="status-dot online" id="statusDot"></span>
            <small class="text-muted" id="statusText">API Connectée</small>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <script>
        // Configuration globale
        const API_BASE_URL = 'http://localhost:8081/api';
        let currentUser = null;
        let currentSection = 'dashboard';
        let charts = {};
        let dataTable = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initialisation du Dashboard Professionnel');

            // Vérifier si l'utilisateur est connecté
            const savedToken = localStorage.getItem('authToken');
            const savedUser = localStorage.getItem('userInfo');

            if (savedToken && savedUser) {
                currentUser = JSON.parse(savedUser);
                document.getElementById('userName').textContent = `${currentUser.prenom} ${currentUser.nom}`;
                checkAPIConnection();
                loadDashboardData();
            } else {
                // Rediriger vers la page de connexion
                window.location.href = 'login-professionnel.html';
            }
        });

        // Vérification de la connexion API
        async function checkAPIConnection() {
            try {
                const response = await axios.get(`${API_BASE_URL}/test`);
                updateAPIStatus(true);
                console.log('✅ API connectée:', response.data.message);
            } catch (error) {
                updateAPIStatus(false);
                console.error('❌ API déconnectée:', error.message);
            }
        }

        // Mise à jour du statut API
        function updateAPIStatus(connected) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');

            if (connected) {
                statusDot.className = 'status-dot online';
                statusText.textContent = 'API Connectée';
            } else {
                statusDot.className = 'status-dot offline';
                statusText.textContent = 'API Déconnectée';
            }
        }

        // Chargement des données du dashboard
        async function loadDashboardData() {
            try {
                console.log('📊 Chargement des données...');

                const [statsResponse, operationsResponse, usersResponse] = await Promise.all([
                    axios.get(`${API_BASE_URL}/operations/statistics`),
                    axios.get(`${API_BASE_URL}/operations`),
                    axios.get(`${API_BASE_URL}/utilisateurs`)
                ]);

                const statistics = statsResponse.data;
                const operations = operationsResponse.data;
                const users = usersResponse.data;

                createDashboardContent(statistics, operations, users);
                console.log('✅ Données chargées avec succès');

            } catch (error) {
                console.error('❌ Erreur de chargement:', error);
                showErrorContent();
            }
        }

        // Création du contenu du dashboard
        function createDashboardContent(statistics, operations, users) {
            const content = `
                <!-- En-tête de section -->
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h1 class="h3 fw-bold mb-1">📊 Vue d'ensemble</h1>
                        <p class="text-muted mb-0">Tableau de bord analytique en temps réel</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser
                        </button>
                        <button class="btn btn-primary" onclick="exportData()">
                            <i class="fas fa-download me-1"></i>
                            Exporter
                        </button>
                    </div>
                </div>

                <!-- KPI Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="kpi-card fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="kpi-value">${statistics.totalOperations?.toLocaleString('fr-FR') || '0'}</div>
                            <div class="kpi-label">Total Opérations</div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="kpi-card success fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="kpi-value">${formatCurrency(statistics.totalMontant || 0)}</div>
                            <div class="kpi-label">Chiffre d'Affaires</div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="kpi-card warning fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="kpi-value">${formatCurrency(statistics.montantMoyen || 0)}</div>
                            <div class="kpi-label">Ticket Moyen</div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="kpi-card danger fade-in" onclick="animateCard(this)">
                            <div class="kpi-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="kpi-value">${statistics.clientsUniques || '0'}</div>
                            <div class="kpi-label">Clients Actifs</div>
                        </div>
                    </div>
                </div>

                <!-- Graphiques -->
                <div class="row mb-4">
                    <div class="col-lg-8 mb-4">
                        <div class="card fade-in">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line text-primary me-2"></i>
                                    Évolution des Opérations
                                </h5>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary active" onclick="changeChartPeriod('7d')">7J</button>
                                    <button class="btn btn-outline-primary" onclick="changeChartPeriod('30d')">30J</button>
                                    <button class="btn btn-outline-primary" onclick="changeChartPeriod('90d')">90J</button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="evolutionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 mb-4">
                        <div class="card fade-in">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie text-success me-2"></i>
                                    Répartition par Type
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="typeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tableau des opérations récentes -->
                <div class="row">
                    <div class="col-12">
                        <div class="card fade-in">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-table text-info me-2"></i>
                                    Opérations Récentes
                                </h5>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-success" onclick="exportExcel()">
                                        <i class="fas fa-file-excel me-1"></i>
                                        Excel
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="exportPDF()">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        PDF
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover" id="operationsTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Type</th>
                                                <th>Client</th>
                                                <th>Produit</th>
                                                <th>Montant</th>
                                                <th>Date</th>
                                                <th>Statut</th>
                                                <th>Canal</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${generateOperationsRows(operations)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('dashboardContent').innerHTML = content;

            // Initialiser les graphiques et le tableau
            setTimeout(() => {
                initializeCharts(statistics);
                initializeDataTable();
            }, 100);
        }

        // Génération des lignes du tableau
        function generateOperationsRows(operations) {
            return operations.slice(0, 20).map(op => `
                <tr>
                    <td><span class="badge bg-secondary">${op.id}</span></td>
                    <td><span class="badge ${getTypeBadgeClass(op.type)}">${op.type}</span></td>
                    <td>${op.clientNom}</td>
                    <td>${op.produitNom}</td>
                    <td class="fw-bold">${op.montant.toLocaleString('fr-FR')}€</td>
                    <td>${formatDate(op.date)}</td>
                    <td><span class="badge ${getStatutBadgeClass(op.statut)}">${op.statut}</span></td>
                    <td><span class="badge badge-info">${op.canal}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewOperation(${op.id})" title="Voir">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editOperation(${op.id})" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // Fonctions utilitaires
        function formatCurrency(amount) {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount || 0);
        }

        function formatDate(dateStr) {
            return new Date(dateStr).toLocaleDateString('fr-FR');
        }

        function getTypeBadgeClass(type) {
            const classes = {
                'SOUSCRIPTION': 'badge-success',
                'SINISTRE': 'badge-danger',
                'AVENANT': 'badge-warning',
                'RESILIATION': 'bg-secondary'
            };
            return classes[type] || 'bg-secondary';
        }

        function getStatutBadgeClass(statut) {
            const classes = {
                'VALIDEE': 'badge-success',
                'EN_COURS': 'badge-warning',
                'EN_ATTENTE': 'bg-secondary'
            };
            return classes[statut] || 'bg-secondary';
        }

        // Animation des cartes KPI
        function animateCard(card) {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
        }

        // Initialisation des graphiques
        function initializeCharts(statistics) {
            // Graphique d'évolution
            const evolutionCtx = document.getElementById('evolutionChart');
            if (evolutionCtx) {
                charts.evolution = new Chart(evolutionCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
                        datasets: [{
                            label: 'Opérations',
                            data: [12, 19, 15, 25, 22, 18, 24],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#3b82f6',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        elements: {
                            point: {
                                hoverRadius: 8
                            }
                        }
                    }
                });
            }

            // Graphique en secteurs
            const typeCtx = document.getElementById('typeChart');
            if (typeCtx && statistics.statsByType) {
                const typeData = statistics.statsByType;
                const labels = Object.keys(typeData).map(type => getTypeLabel(type));
                const data = Object.values(typeData);

                charts.type = new Chart(typeCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: [
                                '#3b82f6',
                                '#10b981',
                                '#f59e0b',
                                '#ef4444'
                            ],
                            borderWidth: 0,
                            hoverOffset: 10
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true,
                                    font: {
                                        size: 12
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }

        function getTypeLabel(type) {
            const labels = {
                'SOUSCRIPTION': 'Souscriptions',
                'SINISTRE': 'Sinistres',
                'AVENANT': 'Avenants',
                'RESILIATION': 'Résiliations'
            };
            return labels[type] || type;
        }

        // Initialisation du DataTable
        function initializeDataTable() {
            if ($.fn.DataTable.isDataTable('#operationsTable')) {
                $('#operationsTable').DataTable().destroy();
            }

            dataTable = $('#operationsTable').DataTable({
                responsive: true,
                pageLength: 10,
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json'
                },
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success btn-sm'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger btn-sm'
                    }
                ],
                columnDefs: [
                    { orderable: false, targets: -1 }
                ]
            });
        }

        // Fonctions d'action
        function showSection(section) {
            currentSection = section;

            // Mise à jour de la navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');

            // Chargement du contenu selon la section
            switch(section) {
                case 'dashboard':
                    loadDashboardData();
                    break;
                case 'operations':
                    loadOperationsSection();
                    break;
                case 'utilisateurs':
                    loadUsersSection();
                    break;
                default:
                    loadDashboardData();
            }
        }

        function refreshData() {
            console.log('🔄 Actualisation des données...');
            loadDashboardData();
        }

        function exportData() {
            console.log('📤 Export des données...');
            // Implémentation de l'export
        }

        function exportExcel() {
            if (dataTable) {
                dataTable.button('.buttons-excel').trigger();
            }
        }

        function exportPDF() {
            if (dataTable) {
                dataTable.button('.buttons-pdf').trigger();
            }
        }

        function viewOperation(id) {
            console.log('👁️ Voir opération:', id);
            // Implémentation de la vue détaillée
        }

        function editOperation(id) {
            console.log('✏️ Modifier opération:', id);
            // Implémentation de l'édition
        }

        function changeChartPeriod(period) {
            console.log('📅 Changement de période:', period);
            // Mise à jour du graphique selon la période
        }

        function logout() {
            console.log('👋 Déconnexion...');
            // Implémentation de la déconnexion
        }

        // Affichage d'erreur
        function showErrorContent() {
            document.getElementById('dashboardContent').innerHTML = `
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-muted">Erreur de chargement</h3>
                    <p class="text-muted">Impossible de charger les données. Vérifiez que l'API est accessible.</p>
                    <button class="btn btn-primary" onclick="loadDashboardData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Réessayer
                    </button>
                </div>
            `;
        }

        // Vérification périodique de l'API
        setInterval(checkAPIConnection, 30000);

        console.log('🏢 Dashboard Assurance Professionnel - Prêt !');
    </script>
</body>
</html>
