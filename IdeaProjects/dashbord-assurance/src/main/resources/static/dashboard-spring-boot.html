<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Dashboard Assurance - Spring Boot API</title>
    
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <style>
        :root {
            --primary: #2563eb;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --light: #f8fafc;
            --dark: #0f172a;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            color: var(--dark);
        }
        
        /* Login Screen */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-primary);
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-icon {
            width: 64px;
            height: 64px;
            background: var(--gradient-primary);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
            box-shadow: var(--shadow);
        }
        
        .btn-login {
            width: 100%;
            padding: 0.875rem;
            background: var(--gradient-primary);
            border: none;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .demo-accounts {
            background: var(--light);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid var(--border);
        }
        
        .demo-account:hover {
            background: var(--primary);
            color: white;
        }
        
        /* Dashboard Container */
        .dashboard-container {
            background: var(--light);
            min-height: 100vh;
            display: none;
        }
        
        .dashboard-container.show {
            display: block;
        }
        
        /* Header Dashboard */
        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border);
            padding: 1.5rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }
        
        /* Sidebar */
        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid var(--border);
            height: calc(100vh - 80px);
            position: fixed;
            left: 0;
            top: 80px;
            overflow-y: auto;
            transition: all 0.3s ease;
            z-index: 999;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        
        .sidebar-toggle {
            position: absolute;
            top: 1rem;
            right: -15px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow);
        }
        
        .nav-item {
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid var(--border);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .nav-item:hover, .nav-item.active {
            background: var(--light);
            border-left: 4px solid var(--primary);
        }
        
        .nav-icon {
            width: 20px;
            text-align: center;
        }
        
        .nav-text {
            transition: opacity 0.3s ease;
        }
        
        .sidebar.collapsed .nav-text {
            opacity: 0;
            display: none;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .main-content.expanded {
            margin-left: 70px;
        }
        
        .kpi-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid var(--border);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 1rem;
        }
        
        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }
        
        .kpi-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }
        
        .chart-container {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--border);
            box-shadow: var(--shadow);
            margin-bottom: 1rem;
        }
        
        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 400px;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: var(--shadow);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification-success {
            background: var(--success);
            color: white;
        }
        
        .notification-error {
            background: var(--danger);
            color: white;
        }
        
        .notification-info {
            background: var(--info);
            color: white;
        }
        
        .notification-warning {
            background: var(--warning);
            color: white;
        }
        
        .api-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 1000;
            cursor: pointer;
        }
        
        .api-status.connected {
            background: var(--success);
            color: white;
        }
        
        .api-status.disconnected {
            background: var(--danger);
            color: white;
        }
        
        .api-status.checking {
            background: var(--warning);
            color: white;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .login-card {
                margin: 1rem;
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- API Status Indicator -->
    <div id="apiStatus" class="api-status checking" onclick="checkAPIConnection()">
        <i class="fas fa-spinner fa-spin me-1"></i>
        Vérification Spring Boot API...
    </div>

    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card fade-in">
            <div class="text-center mb-4">
                <div class="login-icon">
                    <i class="fas fa-leaf"></i>
                </div>
                <h2 class="h3 fw-bold text-dark mb-2">Connexion</h2>
                <p class="text-muted">🍃 Dashboard Assurance - Spring Boot API</p>
            </div>
            
            <form id="loginForm">
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Mot de passe</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                
                <button type="submit" class="btn-login" id="loginBtn">
                    <span id="loginText">Se connecter</span>
                    <span id="loginSpinner" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </span>
                </button>
            </form>
            
            <div id="loginAlert" class="alert" style="display: none;"></div>
            
            <div class="demo-accounts">
                <h6 class="text-muted text-uppercase small fw-bold mb-2">🎯 Comptes de test (Spring Boot)</h6>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'admin123')">
                    <span class="small">🍃 Administrateur</span>
                    <span class="badge bg-danger">ADMIN</span>
                </div>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'gestionnaire123')">
                    <span class="small">📊 Gestionnaire</span>
                    <span class="badge bg-warning">GESTIONNAIRE</span>
                </div>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'conseiller123')">
                    <span class="small">💼 Conseiller</span>
                    <span class="badge bg-info">CONSEILLER</span>
                </div>
                <div class="demo-account" onclick="fillLogin('<EMAIL>', 'client123')">
                    <span class="small">👤 Client</span>
                    <span class="badge bg-success">CLIENT</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Screen -->
    <div id="dashboardScreen" class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <button class="btn btn-outline-primary me-3 d-md-none" onclick="toggleSidebar()">
                                <i class="fas fa-bars"></i>
                            </button>
                            <div class="login-icon me-3" style="width: 48px; height: 48px; font-size: 1.5rem;">
                                <i class="fas fa-leaf"></i>
                            </div>
                            <div>
                                <h1 class="h4 mb-0 fw-bold">🍃 AssuranceBoard Spring</h1>
                                <p class="text-muted mb-0 small">Dashboard connecté à Spring Boot API</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-end">
                            <div class="me-3">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                        <span class="text-white fw-bold small" id="userInitials">U</span>
                                    </div>
                                    <div>
                                        <div class="fw-bold small" id="userName">Utilisateur</div>
                                        <div class="text-muted small" id="userRole">Rôle</div>
                                    </div>
                                </div>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary btn-sm" onclick="refreshData()" title="Actualiser">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button class="btn btn-success btn-sm" onclick="exportExcel()" title="Export Excel">
                                    <i class="fas fa-file-excel"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="exportPDF()" title="Export PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="logout()" title="Déconnexion">
                                    <i class="fas fa-sign-out-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-chevron-left" id="toggleIcon"></i>
            </button>
            
            <div class="nav-item active" onclick="showSection('dashboard')">
                <i class="fas fa-tachometer-alt nav-icon"></i>
                <span class="nav-text">📊 Dashboard</span>
            </div>
            <div class="nav-item" onclick="showSection('operations')">
                <i class="fas fa-list nav-icon"></i>
                <span class="nav-text">📋 Opérations</span>
            </div>
            <div class="nav-item" onclick="showSection('utilisateurs')">
                <i class="fas fa-users nav-icon"></i>
                <span class="nav-text">👥 Utilisateurs</span>
            </div>
            <div class="nav-item" onclick="showSection('rapports')">
                <i class="fas fa-chart-bar nav-icon"></i>
                <span class="nav-text">📈 Rapports</span>
            </div>
            <div class="nav-item" onclick="showSection('parametres')">
                <i class="fas fa-cog nav-icon"></i>
                <span class="nav-text">⚙️ Paramètres</span>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="mainContent">
            <div id="dashboardContent">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">🍃 Chargement des données depuis Spring Boot API...</p>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Variables globales
        let currentUser = null;
        let authToken = null;
        let charts = {};
        let statistics = {};
        let operations = [];
        let sidebarCollapsed = false;
        let currentSection = 'dashboard';
        
        // Configuration API Spring Boot
        const API_BASE_URL = 'http://localhost:8080/api';
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🍃 Initialisation du Dashboard Spring Boot');
            setupLoginForm();
            checkAPIConnection();
            checkExistingAuth();
        });
        
        // Vérifier la connexion API Spring Boot
        async function checkAPIConnection() {
            updateAPIStatus('checking');
            try {
                console.log('🔍 Vérification de la connexion Spring Boot API...');
                const response = await axios.get(`${API_BASE_URL}/test`, {
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                updateAPIStatus('connected');
                console.log('✅ Spring Boot API connectée');
                showNotification('🍃 Spring Boot API connectée avec succès', 'success');
                return true;
            } catch (error) {
                updateAPIStatus('disconnected');
                console.error('❌ Spring Boot API déconnectée:', error.message);

                if (error.code === 'ECONNREFUSED' || error.message.includes('Network Error')) {
                    showNotification('⚠️ Spring Boot API déconnectée - Vérifiez que le serveur est démarré sur le port 8080', 'warning');
                } else {
                    showNotification('⚠️ Erreur de connexion à l\'API Spring Boot', 'warning');
                }
                return false;
            }
        }

        // Mettre à jour le statut de l'API
        function updateAPIStatus(status) {
            const apiStatus = document.getElementById('apiStatus');
            apiStatus.className = `api-status ${status}`;

            switch(status) {
                case 'connected':
                    apiStatus.innerHTML = '<i class="fas fa-circle me-1"></i>Spring Boot API Connectée';
                    break;
                case 'disconnected':
                    apiStatus.innerHTML = '<i class="fas fa-circle me-1"></i>Spring Boot API Déconnectée';
                    break;
                case 'checking':
                    apiStatus.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Vérification...';
                    break;
            }
        }

        // Vérifier l'authentification existante
        function checkExistingAuth() {
            const savedToken = localStorage.getItem('springBootAuthToken');
            const savedUser = localStorage.getItem('springBootUserInfo');

            if (savedToken && savedUser) {
                authToken = savedToken;
                currentUser = JSON.parse(savedUser);

                // Configurer Axios avec le token JWT
                axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;

                // Vérifier si le token est encore valide
                verifyToken().then(valid => {
                    if (valid) {
                        showDashboard();
                        showNotification('🔄 Session Spring Boot restaurée', 'info');
                    } else {
                        clearAuthData();
                    }
                });
            }
        }

        // Vérifier la validité du token
        async function verifyToken() {
            try {
                const response = await axios.post(`${API_BASE_URL}/auth/verify`, {}, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                return response.data.success;
            } catch (error) {
                console.log('Token invalide ou expiré');
                return false;
            }
        }

        // Configuration du formulaire de connexion
        function setupLoginForm() {
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);
        }

        // Gestion de la connexion Spring Boot
        async function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginSpinner = document.getElementById('loginSpinner');
            const loginAlert = document.getElementById('loginAlert');

            // Vérifier d'abord la connexion API
            const apiConnected = await checkAPIConnection();
            if (!apiConnected) {
                showLoginError('❌ Impossible de se connecter à l\'API Spring Boot. Vérifiez que le serveur est démarré sur le port 8080.');
                return;
            }

            // Afficher le spinner
            loginBtn.disabled = true;
            loginText.style.display = 'none';
            loginSpinner.style.display = 'inline-block';
            loginAlert.style.display = 'none';

            try {
                console.log('🔐 Tentative de connexion Spring Boot API:', email);

                const response = await axios.post(`${API_BASE_URL}/auth/login`, {
                    email: email,
                    motDePasse: password
                }, {
                    timeout: 15000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log('📡 Réponse Spring Boot API:', response.data);

                if (response.data.success && response.data.token) {
                    // Stocker les informations d'authentification
                    authToken = response.data.token;
                    currentUser = response.data.utilisateur;

                    localStorage.setItem('springBootAuthToken', authToken);
                    localStorage.setItem('springBootUserInfo', JSON.stringify(currentUser));

                    // Configurer Axios avec le token JWT
                    axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;

                    // Afficher le dashboard
                    showDashboard();

                    showNotification(`🎉 Connexion Spring Boot réussie ! Bienvenue ${currentUser.prenom}`, 'success');
                    console.log('✅ Connexion Spring Boot API réussie:', currentUser);

                } else {
                    showLoginError('❌ Erreur de connexion - Réponse invalide du serveur');
                }

            } catch (error) {
                console.error('❌ Erreur de connexion Spring Boot API:', error);

                if (error.response) {
                    const status = error.response.status;
                    const message = error.response.data?.message || 'Erreur inconnue';

                    if (status === 401) {
                        showLoginError('❌ Email ou mot de passe incorrect');
                    } else if (status === 403) {
                        showLoginError('❌ Compte désactivé ou accès refusé');
                    } else {
                        showLoginError(`❌ Erreur serveur (${status}): ${message}`);
                    }
                } else if (error.code === 'ECONNREFUSED' || error.message.includes('Network Error')) {
                    showLoginError('❌ Impossible de se connecter à l\'API Spring Boot. Vérifiez que le serveur est démarré sur le port 8080.');
                    updateAPIStatus('disconnected');
                } else if (error.code === 'ECONNABORTED') {
                    showLoginError('❌ Timeout de connexion. Le serveur Spring Boot met trop de temps à répondre.');
                } else {
                    showLoginError(`❌ Erreur de connexion: ${error.message}`);
                }
            } finally {
                // Masquer le spinner
                loginBtn.disabled = false;
                loginText.style.display = 'inline-block';
                loginSpinner.style.display = 'none';
            }
        }

        // Afficher une erreur de connexion
        function showLoginError(message) {
            const loginAlert = document.getElementById('loginAlert');
            loginAlert.className = 'alert alert-danger';
            loginAlert.textContent = message;
            loginAlert.style.display = 'block';
        }

        // Remplir automatiquement les champs de connexion
        function fillLogin(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            showNotification('📝 Champs remplis automatiquement - Cliquez sur "Se connecter"', 'info');
        }

        // Effacer les données d'authentification
        function clearAuthData() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('springBootAuthToken');
            localStorage.removeItem('springBootUserInfo');
            delete axios.defaults.headers.common['Authorization'];
        }

        // Afficher le dashboard
        function showDashboard() {
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('dashboardScreen').classList.add('show');

            updateUserInterface();
            loadDashboardData();
        }

        // Mise à jour de l'interface utilisateur
        function updateUserInterface() {
            if (!currentUser) return;

            document.getElementById('userName').textContent = `${currentUser.prenom} ${currentUser.nom}`;
            document.getElementById('userRole').textContent = `🍃 ${currentUser.role}`;

            // Mettre à jour l'avatar avec les initiales
            const initials = `${currentUser.prenom?.charAt(0) || ''}${currentUser.nom?.charAt(0) || ''}`;
            document.getElementById('userInitials').textContent = initials.toUpperCase();
        }

        // Chargement des données du dashboard depuis l'API Spring Boot
        async function loadDashboardData() {
            try {
                console.log('📡 Chargement des données depuis Spring Boot API...');

                // Charger les statistiques et opérations depuis l'API Spring Boot
                const [statsResponse, operationsResponse, usersResponse] = await Promise.all([
                    axios.get(`${API_BASE_URL}/operations/statistics`),
                    axios.get(`${API_BASE_URL}/operations`),
                    axios.get(`${API_BASE_URL}/utilisateurs`)
                ]);

                statistics = statsResponse.data;
                operations = operationsResponse.data;

                console.log('✅ Données Spring Boot API chargées:', statistics, operations.length);

                // Créer le contenu du dashboard avec les vraies données
                createDashboardContent();

                showNotification('📊 Données chargées depuis Spring Boot API', 'success');

            } catch (error) {
                console.error('❌ Erreur de chargement Spring Boot API:', error);
                showNotification('⚠️ Erreur de chargement des données', 'warning');

                // Afficher un message d'erreur
                document.getElementById('dashboardContent').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Erreur Spring Boot API :</strong> ${error.message}
                        <br><small>Vérifiez que le serveur Spring Boot est démarré sur le port 8080</small>
                    </div>
                `;
            }
        }

        // Créer le contenu du dashboard avec les vraies données Spring Boot
        function createDashboardContent() {
            const content = `
                <div class="fade-in">
                    <h1 class="h3 fw-bold mb-1">📊 Tableau de bord analytique</h1>
                    <p class="text-muted mb-4">Données en temps réel depuis Spring Boot API</p>
                </div>

                <!-- KPI Cards avec vraies données -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="kpi-card fade-in" onclick="animateKPI(this)">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h2 class="h3 mb-0 text-primary">${statistics.totalOperations?.toLocaleString('fr-FR') || '0'}</h2>
                                    <p class="text-muted mb-0 small">📋 Total Opérations</p>
                                </div>
                                <div class="text-primary">
                                    <i class="fas fa-chart-line fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="kpi-card fade-in" onclick="animateKPI(this)">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h2 class="h3 mb-0 text-success">${formatCurrency(statistics.totalMontant || 0)}</h2>
                                    <p class="text-muted mb-0 small">💰 Volume Total</p>
                                </div>
                                <div class="text-success">
                                    <i class="fas fa-euro-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="kpi-card fade-in" onclick="animateKPI(this)">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h2 class="h3 mb-0 text-warning">${formatCurrency(statistics.montantMoyen || 0)}</h2>
                                    <p class="text-muted mb-0 small">🧮 Ticket Moyen</p>
                                </div>
                                <div class="text-warning">
                                    <i class="fas fa-calculator fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="kpi-card fade-in" onclick="animateKPI(this)">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h2 class="h3 mb-0 text-danger">${statistics.clientsUniques || '0'}</h2>
                                    <p class="text-muted mb-0 small">👥 Clients Actifs</p>
                                </div>
                                <div class="text-danger">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts avec vraies données -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie text-primary me-2"></i>
                                    🥧 Répartition par type (Spring Boot)
                                </h5>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="downloadChart('typeChart')" title="Télécharger">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" onclick="refreshChart('typeChart')" title="Actualiser">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <canvas id="typeChart" height="300"></canvas>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="chart-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar text-success me-2"></i>
                                    🏆 Top clients (Spring Boot)
                                </h5>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-success" onclick="downloadChart('clientChart')" title="Télécharger">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="refreshChart('clientChart')" title="Actualiser">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <canvas id="clientChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Tableau des dernières opérations -->
                <div class="row">
                    <div class="col-12">
                        <div class="chart-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-table text-info me-2"></i>
                                    📊 Dernières opérations (Spring Boot API)
                                </h5>
                                <div>
                                    <button class="btn btn-sm btn-success me-2" onclick="exportExcel()">
                                        <i class="fas fa-file-excel me-1"></i>
                                        Excel
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="exportPDF()">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        PDF
                                    </button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover" id="operationsTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>🆔 ID</th>
                                            <th>📋 Type</th>
                                            <th>👤 Client</th>
                                            <th>📦 Produit</th>
                                            <th>💰 Montant</th>
                                            <th>📅 Date</th>
                                            <th>📊 Statut</th>
                                            <th>📡 Canal</th>
                                            <th>⚙️ Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${generateOperationsTableRows()}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Message de succès API -->
                <div class="alert alert-success mt-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>✅ Connexion Spring Boot API réussie !</strong> Dashboard chargé avec les vraies données depuis l'API Spring Boot.
                    <br><small>🍃 API: ${API_BASE_URL} | 📊 ${operations.length} opérations | 💰 ${formatCurrency(statistics.totalMontant)} de volume</small>
                </div>
            `;

            document.getElementById('dashboardContent').innerHTML = content;

            // Créer les graphiques avec les vraies données après un délai
            setTimeout(() => {
                createChartsWithRealData();
                initializeDataTable();
            }, 100);
        }

        console.log('🍃 Dashboard Assurance - Spring Boot API');
        console.log('🔗 API:', API_BASE_URL);
        console.log('✅ Prêt pour la connexion !');
    </script>
</body>
</html>
