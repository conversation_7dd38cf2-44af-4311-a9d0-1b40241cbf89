<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Conseiller IA - Assurance</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
    <!-- Axios -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    
    <style>
        :root {
            --primary: #1e40af;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --info: #0891b2;
            --light: #f8fafc;
            --dark: #0f172a;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
            --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light);
            color: var(--dark);
        }

        .navbar {
            background: white !important;
            box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--primary) !important;
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 4px -1px rgb(0 0 0 / 0.1);
            padding: 0;
            position: fixed;
            top: 76px;
            left: 0;
            width: 280px;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            background: var(--gradient-primary);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 2rem;
            min-height: calc(100vh - 76px);
        }

        .alert-card {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 1rem;
            border-left: 4px solid var(--warning);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .alert-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 12px -1px rgb(0 0 0 / 0.15);
        }

        .alert-card.urgent {
            border-left-color: var(--danger);
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(220, 38, 38, 0.05) 100%);
        }

        .alert-card.info {
            border-left-color: var(--info);
            background: linear-gradient(135deg, rgba(8, 145, 178, 0.05) 0%, rgba(14, 116, 144, 0.05) 100%);
        }

        .alert-card.success {
            border-left-color: var(--success);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
        }

        .suggestion-card {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 1rem;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .suggestion-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .suggestion-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
        }

        .ai-badge {
            background: var(--gradient-primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .prediction-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .prediction-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-success);
        }

        .client-360-modal .modal-dialog {
            max-width: 90vw;
        }

        .client-360-content {
            max-height: 80vh;
            overflow-y: auto;
        }

        .performance-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .performance-indicator.positive {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .performance-indicator.negative {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        .performance-indicator.neutral {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .chart-container.large {
            height: 400px;
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .nav-pills .nav-link {
            color: var(--dark);
            padding: 0.875rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .nav-pills .nav-link:hover {
            background: rgba(30, 64, 175, 0.1);
            color: var(--primary);
        }

        .nav-pills .nav-link.active {
            background: var(--gradient-primary);
            color: white;
        }

        .btn {
            font-weight: 500;
            border-radius: 0.5rem;
            padding: 0.625rem 1.25rem;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .btn-success {
            background: var(--gradient-success);
            color: white;
        }

        .btn-warning {
            background: var(--gradient-warning);
            color: white;
        }

        .btn-danger {
            background: var(--gradient-danger);
            color: white;
        }

        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 1.5rem;
            background: white;
        }

        .card-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            padding: 1.5rem;
            font-weight: 600;
            border-radius: 1rem 1rem 0 0 !important;
        }

        .badge {
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
        }

        .table thead th {
            background: var(--light);
            border: none;
            font-weight: 600;
            color: var(--dark);
            padding: 1rem;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
        }

        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(30, 64, 175, 0.05);
        }

        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container-fluid">
            <button class="btn btn-outline-primary d-lg-none me-3" type="button" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            
            <a class="navbar-brand" href="#">
                <i class="fas fa-brain me-2"></i>
                Dashboard Conseiller IA
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="user-avatar me-2" style="width: 32px; height: 32px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">C</div>
                        <span>Conseiller Expert</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" onclick="showProfile()">
                            <i class="fas fa-user me-2"></i>Profil
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()">
                            <i class="fas fa-cog me-2"></i>Paramètres IA
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="login.html">
                            <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="d-flex align-items-center">
                <div class="user-avatar me-3" style="width: 48px; height: 48px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 1.25rem;">C</div>
                <div>
                    <div class="fw-bold">Conseiller Expert</div>
                    <small>Spécialiste IA & Prédictif</small>
                </div>
            </div>
        </div>
        
        <nav class="nav nav-pills flex-column mt-3">
            <a class="nav-link active" href="#" onclick="showSection('accueil')">
                <i class="fas fa-home"></i>
                🏠 Accueil Intelligent
            </a>
            <a class="nav-link" href="#" onclick="showSection('suggestions')">
                <i class="fas fa-lightbulb"></i>
                💡 Suggestions IA
            </a>
            <a class="nav-link" href="#" onclick="showSection('predictions')">
                <i class="fas fa-crystal-ball"></i>
                🔮 Analyse Prédictive
            </a>
            <a class="nav-link" href="#" onclick="showSection('rapports')">
                <i class="fas fa-file-pdf"></i>
                📄 Rapports Auto
            </a>
            <a class="nav-link" href="#" onclick="showSection('clients')">
                <i class="fas fa-users"></i>
                👥 Vision 360° Clients
            </a>
            <a class="nav-link" href="#" onclick="showSection('parametres')">
                <i class="fas fa-sliders-h"></i>
                ⚙️ Paramètres IA
            </a>
        </nav>
    </div>

    <!-- Contenu principal -->
    <div class="main-content">
        <div id="mainContent">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement de l'IA...</span>
                </div>
                <p class="mt-2">Initialisation du système d'intelligence artificielle...</p>
            </div>
        </div>
    </div>

    <!-- Modal Vision 360° Client -->
    <div class="modal fade client-360-modal" id="client360Modal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-circle me-2"></i>
                        Vision 360° Client
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body client-360-content" id="client360Content">
                    <!-- Contenu dynamique -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

    <script>
        // Configuration des APIs
        const NODE_API_URL = 'http://localhost:8081/api';
        const SPRING_API_URL = 'http://localhost:8080/api';
        
        let currentSection = 'accueil';
        let charts = {};
        let clientsData = [];
        let operationsData = [];
        let alertesData = [];
        let suggestionsIA = [];

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧠 Initialisation Dashboard Conseiller IA');
            loadInitialData();
        });

        // ========================================
        // 1. ACCUEIL INTELLIGENT
        // ========================================

        async function loadInitialData() {
            try {
                console.log('🔄 Chargement des données initiales...');

                // Chargement parallèle des données
                const [statsResponse, operationsResponse, clientsResponse] = await Promise.all([
                    axios.get(`${NODE_API_URL}/operations/statistics`),
                    axios.get(`${NODE_API_URL}/operations`),
                    axios.get(`${SPRING_API_URL}/utilisateurs/clients`)
                ]);

                operationsData = operationsResponse.data;
                clientsData = clientsResponse.data;

                // Génération des alertes intelligentes
                generateAlertes();

                // Génération des suggestions IA
                generateSuggestionsIA();

                // Affichage de l'accueil intelligent
                showAccueilIntelligent();

                console.log('✅ Données chargées et IA initialisée');

            } catch (error) {
                console.error('❌ Erreur de chargement:', error);
                showErrorSection();
            }
        }

        function showAccueilIntelligent() {
            const alertesUrgentes = alertesData.filter(a => a.urgence === 'urgent').length;
            const suggestionsDisponibles = suggestionsIA.length;

            const content = `
                <!-- En-tête intelligent -->
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h2 class="h4 fw-bold mb-1">🏠 Accueil Intelligent</h2>
                        <p class="text-muted mb-0">Tableau de bord IA pour conseiller expert</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="ai-badge">
                            <i class="fas fa-brain"></i>
                            IA Active
                        </span>
                        <button class="btn btn-primary btn-sm" onclick="refreshIA()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser IA
                        </button>
                    </div>
                </div>

                <!-- Résumé des alertes personnalisées -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="alert-card urgent fade-in" onclick="showSection('suggestions')">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="fw-bold text-danger mb-2">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Alertes Urgentes
                                    </h6>
                                    <div class="h4 fw-bold text-danger">${alertesUrgentes}</div>
                                    <small class="text-muted">Nécessitent une action immédiate</small>
                                </div>
                                <div class="pulse">
                                    <i class="fas fa-bell text-danger" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="alert-card info fade-in" onclick="showSection('suggestions')">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="fw-bold text-info mb-2">
                                        <i class="fas fa-lightbulb me-2"></i>
                                        Suggestions IA
                                    </h6>
                                    <div class="h4 fw-bold text-info">${suggestionsDisponibles}</div>
                                    <small class="text-muted">Recommandations personnalisées</small>
                                </div>
                                <div>
                                    <i class="fas fa-robot text-info" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="alert-card success fade-in" onclick="showSection('clients')">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="fw-bold text-success mb-2">
                                        <i class="fas fa-users me-2"></i>
                                        Clients Actifs
                                    </h6>
                                    <div class="h4 fw-bold text-success">${clientsData.length}</div>
                                    <small class="text-muted">Portefeuille sous gestion</small>
                                </div>
                                <div>
                                    <i class="fas fa-chart-line text-success" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alertes détaillées -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-circle text-warning me-2"></i>
                            🚨 Alertes Personnalisées
                        </h5>
                    </div>
                    <div class="card-body">
                        ${generateAlertesHTML()}
                    </div>
                </div>

                <!-- Aperçu des suggestions -->
                <div class="card fade-in mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-brain text-primary me-2"></i>
                            💡 Aperçu des Suggestions IA
                        </h5>
                    </div>
                    <div class="card-body">
                        ${generateSuggestionsPreviewHTML()}
                    </div>
                </div>
            `;

            document.getElementById('mainContent').innerHTML = content;
        }

        // ========================================
        // GÉNÉRATION DES ALERTES INTELLIGENTES
        // ========================================

        function generateAlertes() {
            alertesData = [];

            // Clients inactifs depuis 6 mois
            const clientsInactifs = clientsData.filter(client => {
                // Simulation d'inactivité basée sur l'ID
                return client.id % 3 === 0;
            });

            clientsInactifs.forEach(client => {
                alertesData.push({
                    id: `inactif_${client.id}`,
                    type: 'inactivite',
                    urgence: 'urgent',
                    titre: 'Client inactif détecté',
                    description: `${client.prenom} ${client.nom} n'a effectué aucune opération depuis 6 mois`,
                    client: client,
                    action: 'Contacter pour réactivation',
                    impact: 'Risque de résiliation',
                    dateDetection: new Date()
                });
            });

            // Opérations dépassant un seuil de montant
            const operationsImportantes = operationsData.filter(op => op.montant > 5000);

            operationsImportantes.slice(0, 3).forEach(operation => {
                alertesData.push({
                    id: `montant_${operation.id}`,
                    type: 'montant_eleve',
                    urgence: 'info',
                    titre: 'Opération de montant élevé',
                    description: `Opération de ${operation.montant.toLocaleString('fr-FR')}€ pour ${operation.clientNom}`,
                    operation: operation,
                    action: 'Vérifier et valider',
                    impact: 'Surveillance renforcée',
                    dateDetection: new Date()
                });
            });

            // Arbitrages inhabituels (simulation)
            const arbitragesInhabituels = operationsData.filter(op =>
                op.type === 'AVENANT' && Math.random() > 0.7
            );

            arbitragesInhabituels.forEach(operation => {
                alertesData.push({
                    id: `arbitrage_${operation.id}`,
                    type: 'arbitrage_inhabituel',
                    urgence: 'urgent',
                    titre: 'Arbitrage inhabituel détecté',
                    description: `Arbitrage atypique détecté pour ${operation.clientNom}`,
                    operation: operation,
                    action: 'Analyser et contacter',
                    impact: 'Risque de perte',
                    dateDetection: new Date()
                });
            });

            console.log(`🚨 ${alertesData.length} alertes générées par l'IA`);
        }

        // ========================================
        // 2. SUGGESTIONS D'ACTION IA
        // ========================================

        function generateSuggestionsIA() {
            suggestionsIA = [];

            // Suggestions basées sur les clients
            clientsData.forEach(client => {
                // Suggestion de contact pour arbitrage
                if (client.id % 4 === 1) {
                    suggestionsIA.push({
                        id: `arbitrage_${client.id}`,
                        type: 'arbitrage',
                        priorite: 'haute',
                        titre: 'Opportunité d\'arbitrage détectée',
                        description: `Contacter ${client.prenom} ${client.nom} pour arbitrer son contrat en baisse depuis 3 mois.`,
                        client: client,
                        action: 'Proposer un arbitrage',
                        benefice: 'Optimisation du rendement',
                        probabilite: 85,
                        impactFinancier: '+2,500€',
                        dateGeneration: new Date()
                    });
                }

                // Suggestion de versement complémentaire
                if (client.id % 4 === 2) {
                    suggestionsIA.push({
                        id: `versement_${client.id}`,
                        type: 'versement',
                        priorite: 'moyenne',
                        titre: 'Versement complémentaire recommandé',
                        description: `Proposer un versement complémentaire à ${client.prenom} ${client.nom} dont le contrat est en excédent de performance.`,
                        client: client,
                        action: 'Proposer un versement',
                        benefice: 'Capitalisation sur la performance',
                        probabilite: 72,
                        impactFinancier: '+5,000€',
                        dateGeneration: new Date()
                    });
                }

                // Suggestion de diversification
                if (client.id % 4 === 3) {
                    suggestionsIA.push({
                        id: `diversification_${client.id}`,
                        type: 'diversification',
                        priorite: 'moyenne',
                        titre: 'Diversification du portefeuille',
                        description: `Recommander une diversification à ${client.prenom} ${client.nom} pour réduire les risques.`,
                        client: client,
                        action: 'Proposer diversification',
                        benefice: 'Réduction du risque',
                        probabilite: 68,
                        impactFinancier: 'Stabilité',
                        dateGeneration: new Date()
                    });
                }
            });

            console.log(`💡 ${suggestionsIA.length} suggestions IA générées`);
        }

        function showSuggestionsIA() {
            const content = `
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h2 class="h4 fw-bold mb-1">💡 Suggestions d'Action IA</h2>
                        <p class="text-muted mb-0">Recommandations automatiques basées sur l'analyse des données</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="ai-badge">
                            <i class="fas fa-robot"></i>
                            ${suggestionsIA.length} Suggestions
                        </span>
                        <button class="btn btn-success btn-sm" onclick="executerToutesSuggestions()">
                            <i class="fas fa-play me-1"></i>
                            Exécuter Toutes
                        </button>
                    </div>
                </div>

                <div class="row">
                    ${suggestionsIA.map(suggestion => `
                        <div class="col-lg-6 mb-3">
                            <div class="suggestion-card fade-in">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <span class="badge ${getPrioriteBadgeClass(suggestion.priorite)} mb-2">
                                            ${suggestion.priorite.toUpperCase()}
                                        </span>
                                        <h6 class="fw-bold mb-1">${suggestion.titre}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-brain me-1"></i>
                                            Généré par IA • ${suggestion.probabilite}% de réussite
                                        </small>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="executerSuggestion('${suggestion.id}')">
                                                <i class="fas fa-play me-2"></i>Exécuter
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="reporterSuggestion('${suggestion.id}')">
                                                <i class="fas fa-clock me-2"></i>Reporter
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="ignorerSuggestion('${suggestion.id}')">
                                                <i class="fas fa-times me-2"></i>Ignorer
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>

                                <p class="mb-3">${suggestion.description}</p>

                                <div class="row g-2 mb-3">
                                    <div class="col-6">
                                        <small class="text-muted d-block">Action recommandée</small>
                                        <strong>${suggestion.action}</strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">Bénéfice attendu</small>
                                        <strong class="text-success">${suggestion.impactFinancier}</strong>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="user-avatar" style="width: 32px; height: 32px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.75rem;">
                                            ${suggestion.client.prenom.charAt(0)}${suggestion.client.nom.charAt(0)}
                                        </div>
                                        <span class="fw-medium">${suggestion.client.prenom} ${suggestion.client.nom}</span>
                                    </div>
                                    <button class="btn btn-primary btn-sm" onclick="voirClient360(${suggestion.client.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        Voir Client
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            document.getElementById('mainContent').innerHTML = content;
        }

        // ========================================
        // 3. MODE ANALYSE PRÉDICTIVE
        // ========================================

        function showAnalysePredictive() {
            const content = `
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h2 class="h4 fw-bold mb-1">🔮 Analyse Prédictive</h2>
                        <p class="text-muted mb-0">Projections et tendances basées sur l'intelligence artificielle</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="ai-badge">
                            <i class="fas fa-crystal-ball"></i>
                            Modèle IA v2.1
                        </span>
                        <button class="btn btn-info btn-sm" onclick="recalculerPredictions()">
                            <i class="fas fa-calculator me-1"></i>
                            Recalculer
                        </button>
                    </div>
                </div>

                <!-- Projections principales -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="prediction-card fade-in">
                            <h5 class="fw-bold mb-3">
                                <i class="fas fa-chart-line text-success me-2"></i>
                                📈 Évolution Probable des Arbitrages
                            </h5>
                            <div class="chart-container large">
                                <canvas id="arbitragesPredictionChart"></canvas>
                            </div>
                            <div class="mt-3">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="performance-indicator positive">
                                            <i class="fas fa-arrow-up"></i>
                                            +15% prévu
                                        </div>
                                        <small class="d-block text-muted mt-1">Prochain trimestre</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="performance-indicator neutral">
                                            <i class="fas fa-calendar"></i>
                                            Saisonnalité
                                        </div>
                                        <small class="d-block text-muted mt-1">Pic en décembre</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="performance-indicator positive">
                                            <i class="fas fa-target"></i>
                                            85% fiabilité
                                        </div>
                                        <small class="d-block text-muted mt-1">Modèle IA</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="prediction-card fade-in">
                            <h6 class="fw-bold mb-3">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                🚨 Anticipation des Rachats
                            </h6>
                            <div class="chart-container">
                                <canvas id="rachatsPredictionChart"></canvas>
                            </div>
                            <div class="mt-3">
                                <div class="alert alert-warning">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        <strong>Alerte:</strong> Pic de rachats prévu en septembre (+25%)
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analyses détaillées -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card fade-in">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-brain text-primary me-2"></i>
                                    🤖 Insights IA
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Probabilité de nouveaux contrats</span>
                                        <span class="fw-bold text-success">78%</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-success" style="width: 78%"></div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Risque de résiliations</span>
                                        <span class="fw-bold text-warning">23%</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-warning" style="width: 23%"></div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>Opportunités d'upselling</span>
                                        <span class="fw-bold text-info">65%</span>
                                    </div>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-info" style="width: 65%"></div>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <small>
                                        <i class="fas fa-lightbulb me-1"></i>
                                        <strong>Recommandation IA:</strong> Concentrer les efforts sur les clients âgés de 45-55 ans pour maximiser les conversions.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card fade-in">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-calendar-alt text-info me-2"></i>
                                    📅 Calendrier Prédictif
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <div class="timeline-item mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="timeline-marker bg-success"></div>
                                            <div class="ms-3">
                                                <div class="fw-bold">Juillet 2025</div>
                                                <small class="text-muted">Pic d'arbitrages prévu (+20%)</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="timeline-item mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="timeline-marker bg-warning"></div>
                                            <div class="ms-3">
                                                <div class="fw-bold">Septembre 2025</div>
                                                <small class="text-muted">Attention aux rachats (+25%)</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="timeline-item mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="timeline-marker bg-info"></div>
                                            <div class="ms-3">
                                                <div class="fw-bold">Décembre 2025</div>
                                                <small class="text-muted">Opportunité de fin d'année</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('mainContent').innerHTML = content;

            // Initialiser les graphiques prédictifs
            setTimeout(() => {
                initializePredictionCharts();
            }, 100);
        }

        // ========================================
        // 4. GÉNÉRATION AUTOMATIQUE DE RAPPORTS PDF
        // ========================================

        function showRapportsAuto() {
            const content = `
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h2 class="h4 fw-bold mb-1">📄 Rapports Automatiques</h2>
                        <p class="text-muted mb-0">Génération intelligente de rapports clients personnalisés</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="ai-badge">
                            <i class="fas fa-file-pdf"></i>
                            Auto-génération
                        </span>
                        <button class="btn btn-danger btn-sm" onclick="genererTousRapports()">
                            <i class="fas fa-file-pdf me-1"></i>
                            Générer Tous
                        </button>
                    </div>
                </div>

                <!-- Templates de rapports -->
                <div class="row mb-4">
                    <div class="col-lg-4">
                        <div class="card fade-in">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie text-primary me-2"></i>
                                    📊 Rapport Portefeuille
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3">Analyse complète du portefeuille client avec recommandations IA</p>
                                <ul class="list-unstyled mb-3">
                                    <li><i class="fas fa-check text-success me-2"></i>Répartition des investissements</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Performance historique</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Recommandations IA</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Projections futures</li>
                                </ul>
                                <button class="btn btn-primary w-100" onclick="genererRapportPortefeuille()">
                                    <i class="fas fa-download me-1"></i>
                                    Générer PDF
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card fade-in">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-user-chart text-success me-2"></i>
                                    👤 Rapport Client
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3">Synthèse personnalisée pour entretien client</p>
                                <ul class="list-unstyled mb-3">
                                    <li><i class="fas fa-check text-success me-2"></i>Historique complet</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Objectifs et besoins</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Opportunités détectées</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Plan d'action IA</li>
                                </ul>
                                <button class="btn btn-success w-100" onclick="genererRapportClient()">
                                    <i class="fas fa-download me-1"></i>
                                    Générer PDF
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card fade-in">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-brain text-warning me-2"></i>
                                    🤖 Rapport IA
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3">Analyse prédictive et recommandations avancées</p>
                                <ul class="list-unstyled mb-3">
                                    <li><i class="fas fa-check text-success me-2"></i>Prédictions de marché</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Scoring de risque</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Optimisations suggérées</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Alertes préventives</li>
                                </ul>
                                <button class="btn btn-warning w-100" onclick="genererRapportIA()">
                                    <i class="fas fa-download me-1"></i>
                                    Générer PDF
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rapports récents -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history text-info me-2"></i>
                            📋 Rapports Récents
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>📄 Type</th>
                                        <th>👤 Client</th>
                                        <th>📅 Généré le</th>
                                        <th>📊 Statut</th>
                                        <th>⚙️ Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${generateRapportsRecentsList()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('mainContent').innerHTML = content;
        }

        // ========================================
        // 5. VUE "VISION 360" D'UN CLIENT
        // ========================================

        function voirClient360(clientId) {
            const client = clientsData.find(c => c.id === clientId);
            if (!client) return;

            // Générer des données simulées pour le client
            const clientOperations = operationsData.filter(op =>
                op.clientNom.includes(client.prenom) || op.clientNom.includes(client.nom)
            ).slice(0, 10);

            const content = `
                <!-- En-tête client -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-3">
                            <div class="user-avatar me-3" style="width: 64px; height: 64px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 1.5rem;">
                                ${client.prenom.charAt(0)}${client.nom.charAt(0)}
                            </div>
                            <div>
                                <h4 class="fw-bold mb-1">${client.prenom} ${client.nom}</h4>
                                <p class="text-muted mb-1">${client.email}</p>
                                <span class="badge bg-success">Client Actif</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-primary me-2" onclick="genererRapportClient360(${client.id})">
                            <i class="fas fa-file-pdf me-1"></i>
                            Rapport PDF
                        </button>
                        <button class="btn btn-success" onclick="contacterClient(${client.id})">
                            <i class="fas fa-phone me-1"></i>
                            Contacter
                        </button>
                    </div>
                </div>

                <!-- KPI Client -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-primary fw-bold">${(Math.random() * 100000 + 50000).toFixed(0).toLocaleString('fr-FR')}€</h5>
                                <small class="text-muted">Encours Total</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-success fw-bold">+${(Math.random() * 10 + 2).toFixed(1)}%</h5>
                                <small class="text-muted">Performance YTD</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-info fw-bold">${clientOperations.length}</h5>
                                <small class="text-muted">Opérations</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="text-warning fw-bold">${Math.floor(Math.random() * 5 + 1)}</h5>
                                <small class="text-muted">Contrats</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contenu principal -->
                <div class="row">
                    <!-- Répartition du portefeuille -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-pie text-primary me-2"></i>
                                    🥧 Répartition du Portefeuille
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="portefeuilleChart_${client.id}"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance comparative -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar text-success me-2"></i>
                                    📊 Performance Comparative
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="performanceChart_${client.id}"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Historique des opérations -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-history text-info me-2"></i>
                            📋 Historique des Opérations
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>📅 Date</th>
                                        <th>📋 Type</th>
                                        <th>📦 Produit</th>
                                        <th>💰 Montant</th>
                                        <th>📊 Statut</th>
                                        <th>📡 Canal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${clientOperations.map(op => `
                                        <tr>
                                            <td>${formatDate(op.date)}</td>
                                            <td><span class="badge ${getTypeBadgeClass(op.type)}">${op.type}</span></td>
                                            <td>${op.produitNom}</td>
                                            <td class="fw-bold">${op.montant.toLocaleString('fr-FR')}€</td>
                                            <td><span class="badge ${getStatutBadgeClass(op.statut)}">${op.statut}</span></td>
                                            <td><span class="badge bg-info">${op.canal}</span></td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Recommandations IA pour ce client -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-brain text-warning me-2"></i>
                            🤖 Recommandations IA Personnalisées
                        </h6>
                    </div>
                    <div class="card-body">
                        ${generateRecommandationsClient(client)}
                    </div>
                </div>
            `;

            document.getElementById('client360Content').innerHTML = content;

            // Afficher le modal
            const modal = new bootstrap.Modal(document.getElementById('client360Modal'));
            modal.show();

            // Initialiser les graphiques après affichage du modal
            setTimeout(() => {
                initializeClient360Charts(client);
            }, 300);
        }

        // ========================================
        // FONCTIONS UTILITAIRES ET NAVIGATION
        // ========================================

        function showSection(section) {
            currentSection = section;

            // Mise à jour de la navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');

            // Chargement du contenu selon la section
            switch(section) {
                case 'accueil':
                    showAccueilIntelligent();
                    break;
                case 'suggestions':
                    showSuggestionsIA();
                    break;
                case 'predictions':
                    showAnalysePredictive();
                    break;
                case 'rapports':
                    showRapportsAuto();
                    break;
                case 'clients':
                    showClientsSection();
                    break;
                case 'parametres':
                    showParametresIA();
                    break;
                default:
                    showAccueilIntelligent();
            }
        }

        function showClientsSection() {
            const content = `
                <div class="d-flex justify-content-between align-items-center mb-4 fade-in">
                    <div>
                        <h2 class="h4 fw-bold mb-1">👥 Vision 360° Clients</h2>
                        <p class="text-muted mb-0">Analyse complète et personnalisée de votre portefeuille</p>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="ai-badge">
                            <i class="fas fa-users"></i>
                            ${clientsData.length} Clients
                        </span>
                        <button class="btn btn-primary btn-sm" onclick="exporterPortefeuille()">
                            <i class="fas fa-download me-1"></i>
                            Exporter
                        </button>
                    </div>
                </div>

                <div class="row">
                    ${clientsData.map(client => `
                        <div class="col-lg-6 mb-3">
                            <div class="card fade-in" style="cursor: pointer;" onclick="voirClient360(${client.id})">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3" style="width: 48px; height: 48px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                                                ${client.prenom.charAt(0)}${client.nom.charAt(0)}
                                            </div>
                                            <div>
                                                <h6 class="fw-bold mb-1">${client.prenom} ${client.nom}</h6>
                                                <small class="text-muted">${client.email}</small>
                                            </div>
                                        </div>
                                        <span class="badge ${client.actif ? 'bg-success' : 'bg-secondary'}">
                                            ${client.actif ? 'Actif' : 'Inactif'}
                                        </span>
                                    </div>

                                    <div class="row g-2 mb-3">
                                        <div class="col-6">
                                            <small class="text-muted d-block">Encours</small>
                                            <strong>${(Math.random() * 100000 + 50000).toFixed(0).toLocaleString('fr-FR')}€</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted d-block">Performance</small>
                                            <strong class="text-success">+${(Math.random() * 10 + 2).toFixed(1)}%</strong>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Client depuis ${Math.floor(Math.random() * 5 + 1)} ans
                                        </small>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="event.stopPropagation(); contacterClient(${client.id})" title="Contacter">
                                                <i class="fas fa-phone"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="event.stopPropagation(); genererRapportClient360(${client.id})" title="Rapport">
                                                <i class="fas fa-file-pdf"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            document.getElementById('mainContent').innerHTML = content;
        }

        // Fonctions HTML generators
        function generateAlertesHTML() {
            if (alertesData.length === 0) {
                return '<p class="text-muted text-center py-4">Aucune alerte détectée par l\'IA</p>';
            }

            return alertesData.map(alerte => `
                <div class="alert-card ${alerte.urgence} mb-3" onclick="traiterAlerte('${alerte.id}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge ${getUrgenceBadgeClass(alerte.urgence)} me-2">
                                    ${alerte.urgence.toUpperCase()}
                                </span>
                                <h6 class="fw-bold mb-0">${alerte.titre}</h6>
                            </div>
                            <p class="mb-2">${alerte.description}</p>
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <small class="text-muted d-block">Action recommandée</small>
                                    <strong>${alerte.action}</strong>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted d-block">Impact potentiel</small>
                                    <strong class="text-warning">${alerte.impact}</strong>
                                </div>
                            </div>
                        </div>
                        <div class="ms-3">
                            <i class="fas ${getAlerteIcon(alerte.type)}" style="font-size: 1.5rem; color: ${getAlerteColor(alerte.urgence)};"></i>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function generateSuggestionsPreviewHTML() {
            const topSuggestions = suggestionsIA.slice(0, 3);

            if (topSuggestions.length === 0) {
                return '<p class="text-muted text-center py-4">Aucune suggestion disponible</p>';
            }

            return topSuggestions.map(suggestion => `
                <div class="d-flex align-items-center justify-content-between p-3 border rounded mb-2">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3" style="width: 40px; height: 40px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                            ${suggestion.client.prenom.charAt(0)}${suggestion.client.nom.charAt(0)}
                        </div>
                        <div>
                            <div class="fw-medium">${suggestion.titre}</div>
                            <small class="text-muted">${suggestion.client.prenom} ${suggestion.client.nom} • ${suggestion.probabilite}% de réussite</small>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-primary" onclick="executerSuggestion('${suggestion.id}')">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            `).join('');
        }

        function generateRapportsRecentsList() {
            const rapportsSimules = [
                { type: 'Portefeuille', client: 'Martin Dupont', date: '2025-06-29', statut: 'Généré' },
                { type: 'Client 360°', client: 'Sophie Martin', date: '2025-06-28', statut: 'Envoyé' },
                { type: 'Analyse IA', client: 'Pierre Durand', date: '2025-06-27', statut: 'En cours' }
            ];

            return rapportsSimules.map(rapport => `
                <tr>
                    <td><span class="badge bg-primary">${rapport.type}</span></td>
                    <td>${rapport.client}</td>
                    <td>${rapport.date}</td>
                    <td><span class="badge ${rapport.statut === 'Généré' ? 'bg-success' : rapport.statut === 'Envoyé' ? 'bg-info' : 'bg-warning'}">${rapport.statut}</span></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" title="Télécharger">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-outline-secondary" title="Partager">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function generateRecommandationsClient(client) {
            const recommandations = [
                {
                    type: 'Optimisation',
                    titre: 'Rééquilibrage de portefeuille recommandé',
                    description: 'L\'IA détecte une surpondération en actions. Recommandation de diversification.',
                    priorite: 'haute'
                },
                {
                    type: 'Opportunité',
                    titre: 'Versement complémentaire optimal',
                    description: 'Période favorable pour un versement supplémentaire de 10,000€.',
                    priorite: 'moyenne'
                },
                {
                    type: 'Alerte',
                    titre: 'Révision des objectifs',
                    description: 'Les objectifs actuels peuvent être révisés à la hausse.',
                    priorite: 'basse'
                }
            ];

            return recommandations.map(rec => `
                <div class="alert alert-${rec.priorite === 'haute' ? 'warning' : rec.priorite === 'moyenne' ? 'info' : 'light'} mb-3">
                    <div class="d-flex align-items-start">
                        <i class="fas fa-lightbulb me-3 mt-1"></i>
                        <div>
                            <h6 class="fw-bold mb-1">${rec.titre}</h6>
                            <p class="mb-2">${rec.description}</p>
                            <span class="badge ${getPrioriteBadgeClass(rec.priorite)}">${rec.priorite.toUpperCase()}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // ========================================
        // FONCTIONS UTILITAIRES
        // ========================================

        function formatDate(dateStr) {
            return new Date(dateStr).toLocaleDateString('fr-FR');
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('fr-FR', {
                style: 'currency',
                currency: 'EUR',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount || 0);
        }

        function getPrioriteBadgeClass(priorite) {
            const classes = {
                'haute': 'bg-danger',
                'moyenne': 'bg-warning',
                'basse': 'bg-info'
            };
            return classes[priorite] || 'bg-secondary';
        }

        function getUrgenceBadgeClass(urgence) {
            const classes = {
                'urgent': 'bg-danger',
                'info': 'bg-info',
                'normal': 'bg-success'
            };
            return classes[urgence] || 'bg-secondary';
        }

        function getAlerteIcon(type) {
            const icons = {
                'inactivite': 'fa-user-clock',
                'montant_eleve': 'fa-euro-sign',
                'arbitrage_inhabituel': 'fa-exclamation-triangle'
            };
            return icons[type] || 'fa-bell';
        }

        function getAlerteColor(urgence) {
            const colors = {
                'urgent': '#dc2626',
                'info': '#0891b2',
                'normal': '#059669'
            };
            return colors[urgence] || '#6b7280';
        }

        function getTypeBadgeClass(type) {
            const classes = {
                'SOUSCRIPTION': 'bg-success',
                'SINISTRE': 'bg-danger',
                'AVENANT': 'bg-warning',
                'RESILIATION': 'bg-secondary'
            };
            return classes[type] || 'bg-secondary';
        }

        function getStatutBadgeClass(statut) {
            const classes = {
                'VALIDEE': 'bg-success',
                'EN_COURS': 'bg-warning',
                'EN_ATTENTE': 'bg-secondary'
            };
            return classes[statut] || 'bg-secondary';
        }

        // ========================================
        // GESTIONNAIRES D'ÉVÉNEMENTS
        // ========================================

        // Actions sur les alertes
        function traiterAlerte(alerteId) {
            const alerte = alertesData.find(a => a.id === alerteId);
            if (alerte) {
                alert(`Traitement de l'alerte: ${alerte.titre}\n\nAction: ${alerte.action}`);
            }
        }

        // Actions sur les suggestions
        function executerSuggestion(suggestionId) {
            const suggestion = suggestionsIA.find(s => s.id === suggestionId);
            if (suggestion) {
                alert(`Exécution de la suggestion: ${suggestion.titre}\n\nClient: ${suggestion.client.prenom} ${suggestion.client.nom}\nAction: ${suggestion.action}`);
            }
        }

        function reporterSuggestion(suggestionId) {
            alert('Suggestion reportée à plus tard');
        }

        function ignorerSuggestion(suggestionId) {
            alert('Suggestion ignorée');
        }

        function executerToutesSuggestions() {
            alert(`Exécution de toutes les ${suggestionsIA.length} suggestions IA`);
        }

        // Génération de rapports
        function genererRapportPortefeuille() {
            alert('📊 Génération du rapport portefeuille en cours...\n\nContenu:\n- Répartition des investissements\n- Performance historique\n- Recommandations IA\n- Projections futures');
        }

        function genererRapportClient() {
            alert('👤 Génération du rapport client en cours...\n\nContenu:\n- Historique complet\n- Objectifs et besoins\n- Opportunités détectées\n- Plan d\'action IA');
        }

        function genererRapportIA() {
            alert('🤖 Génération du rapport IA en cours...\n\nContenu:\n- Prédictions de marché\n- Scoring de risque\n- Optimisations suggérées\n- Alertes préventives');
        }

        function genererRapportClient360(clientId) {
            const client = clientsData.find(c => c.id === clientId);
            if (client) {
                alert(`📄 Génération du rapport 360° pour ${client.prenom} ${client.nom}\n\nContenu:\n- Vision complète du portefeuille\n- Analyse de performance\n- Recommandations personnalisées\n- Plan d'action détaillé`);
            }
        }

        function genererTousRapports() {
            alert('📄 Génération de tous les rapports en cours...\n\nRapports générés:\n- Portefeuille global\n- Analyses clients\n- Prédictions IA\n- Synthèses personnalisées');
        }

        // Actions clients
        function contacterClient(clientId) {
            const client = clientsData.find(c => c.id === clientId);
            if (client) {
                alert(`📞 Contact client: ${client.prenom} ${client.nom}\n\nEmail: ${client.email}\nStatut: ${client.actif ? 'Actif' : 'Inactif'}`);
            }
        }

        function exporterPortefeuille() {
            alert('📊 Export du portefeuille en cours...\n\nFormat: Excel\nContenu: Liste complète des clients avec KPI');
        }

        // Actions système
        function refreshIA() {
            alert('🔄 Actualisation de l\'IA en cours...\n\n- Rechargement des données\n- Recalcul des alertes\n- Mise à jour des suggestions\n- Actualisation des prédictions');
            loadInitialData();
        }

        function recalculerPredictions() {
            alert('🔮 Recalcul des prédictions en cours...\n\n- Analyse des tendances\n- Mise à jour des modèles\n- Génération de nouvelles projections');
        }

        function showParametresIA() {
            const content = `
                <div class="text-center py-5">
                    <i class="fas fa-sliders-h text-secondary" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">Paramètres IA</h3>
                    <p class="text-muted">Configuration des algorithmes d'intelligence artificielle</p>
                    <button class="btn btn-primary" onclick="showSection('accueil')">
                        Retour à l'accueil
                    </button>
                </div>
            `;
            document.getElementById('mainContent').innerHTML = content;
        }

        function showErrorSection() {
            const content = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">Erreur de chargement</h3>
                    <p class="text-muted">Impossible de charger les données IA</p>
                    <button class="btn btn-primary" onclick="loadInitialData()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Réessayer
                    </button>
                </div>
            `;
            document.getElementById('mainContent').innerHTML = content;
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        function showProfile() { alert('Profil conseiller'); }
        function showSettings() { alert('Paramètres système'); }

        // Fonctions de graphiques (placeholders)
        function initializePredictionCharts() {
            console.log('📈 Initialisation des graphiques prédictifs');
        }

        function initializeClient360Charts(client) {
            console.log('📊 Initialisation des graphiques client 360°:', client);
        }

        console.log('🧠 Dashboard Conseiller IA - Prêt !');
    </script>
</body>
</html>
