# Configuration Spring Boot Dashboard Assurance
server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: dashboard-assurance
  
  # Configuration de la base de données H2 (pour les tests)
  datasource:
    url: jdbc:h2:mem:dashboarddb
    driverClassName: org.h2.Driver
    username: sa
    password: password
    
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        trace: false
        web-allow-others: false
        
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        
  # Configuration des profils
  profiles:
    active: dev
    
  # Configuration de la sécurité
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# Configuration JWT
jwt:
  secret: dashboardAssuranceSecretKey2024VeryLongAndSecureKey
  expiration: 86400000 # 24 heures en millisecondes
  
# Configuration CORS
cors:
  allowed-origins: 
    - http://localhost:3000
    - http://localhost:8080
    - http://localhost:8081
    - http://127.0.0.1:3000
    - http://127.0.0.1:8080
    - http://127.0.0.1:8081
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# Configuration des logs
logging:
  level:
    com.monsociete.dashbordassurance: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/dashboard-assurance.log

# Configuration de l'application
app:
  name: Dashboard Assurance
  version: 1.0.0
  description: Système de gestion d'assurance avec dashboard analytique
  
  # Configuration des fonctionnalités
  features:
    jwt-auth: true
    user-management: true
    operation-management: true
    statistics: true
    export: true
    filters: true
    
  # Configuration des limites
  limits:
    max-operations-per-page: 100
    max-export-records: 10000
    session-timeout: 1440 # 24 heures en minutes
    
  # Configuration des notifications
  notifications:
    enabled: true
    email:
      enabled: false
      smtp:
        host: localhost
        port: 587
        username: 
        password: 
        
# Configuration Actuator (monitoring)
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,beans,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
    info:
      enabled: true
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# Informations de l'application pour Actuator
info:
  app:
    name: ${app.name}
    version: ${app.version}
    description: ${app.description}
    encoding: UTF-8
    java:
      version: ${java.version}
  build:
    time: 2024-12-30T10:00:00Z
    artifact: dashboard-assurance
    group: com.monsociete
    
---
# Profil de développement
spring:
  config:
    activate:
      on-profile: dev
      
  datasource:
    url: jdbc:h2:mem:dashboarddb
    username: sa
    password: password
    
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop
      
logging:
  level:
    com.monsociete.dashbordassurance: DEBUG
    
---
# Profil de production
spring:
  config:
    activate:
      on-profile: prod
      
  datasource:
    url: ****************************************************
    username: ${DB_USERNAME:dashboard_user}
    password: ${DB_PASSWORD:dashboard_password}
    driver-class-name: org.postgresql.Driver
    
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
    show-sql: false
    
  h2:
    console:
      enabled: false
      
logging:
  level:
    com.monsociete.dashbordassurance: INFO
    root: WARN
  file:
    name: /var/log/dashboard-assurance/application.log
